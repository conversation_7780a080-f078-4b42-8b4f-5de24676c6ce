.AlertRootContainer {
    flex-grow: 1;
}

.AlertMainContainer {
    flex-grow: 1;
    justify-content: flex-end;
    flex-shrink: 0;
    align-self: flex-end;
    margin-right: 20px;
}

.NotificationContainer {
    background-color: rgb(16, 16, 16);
    width: 445px;
    flex-grow: 0;
    margin-top: 8px;
    padding: 8px 16px;
    border-width: 1px;
    border-color: rgba(33, 33, 33, 0);
}

.lable_text {
    color: rgb(255, 255, 255);
    margin: 0;
    padding: 0;
    -unity-text-align: middle-left;
    font-size: 24px;
}

.notification-enter {
    opacity: 0;
    translate: 0 100%;
    transition-property: opacity, translate;
    transition-duration: 0.3s;
    transition-timing-function: ease-out;
}

.notification-exit {
    opacity: 0;
    transition-property: opacity;
    transition-duration: 0.3s;
    transition-timing-function: ease-in;
}