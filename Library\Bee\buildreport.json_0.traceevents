{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754353913393464, "dur": 7001, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754353913400469, "dur": 324599, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754353913725070, "dur": 926, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 55237125, "ts": 1754353913744879, "dur": 11, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913393058, "dur": 28862, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913421921, "dur": 321983, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913421960, "dur": 426, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913422644, "dur": 275952, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913698605, "dur": 630, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913699241, "dur": 3, "ph": "X", "name": "ProcessMessages 515", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913699245, "dur": 35331, "ph": "X", "name": "ReadAsync 515", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913734583, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913734585, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754353913734614, "dur": 9283, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 55237125, "ts": 1754353913744892, "dur": 19, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 47244640256, "ts": 1754353913390468, "dur": 335547, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ts": 1754353913726017, "dur": 97, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 55237125, "ts": 1754353913744913, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 42949672960, "ts": 1754353913379035, "dur": 364902, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754353913379457, "dur": 10893, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754353913743941, "dur": 7, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754353913743950, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 55237125, "ts": 1754353913744922, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754353913422520, "dur":3044, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353913425572, "dur":129, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353913425726, "dur":568, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353913426343, "dur":60, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353913426404, "dur":309015, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353913735421, "dur":139, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353913735757, "dur":3601, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754353913426310, "dur":100, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353913426434, "dur":308984, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353913426332, "dur":85, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353913426417, "dur":300851, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353913735075, "dur":315, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1754353913727269, "dur":8125, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353913426361, "dur":62, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353913426424, "dur":308998, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353913426428, "dur":308989, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353913426411, "dur":4533, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":5, "ts":1754353913430944, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353913431037, "dur":304407, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353913426436, "dur":308980, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353913426470, "dur":308954, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353913426491, "dur":308933, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353913426515, "dur":308920, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353913426540, "dur":308888, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353913426561, "dur":308875, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353913426584, "dur":308847, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353913744145, "dur":384, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 55237125, "ts": 1754353913744960, "dur": 6966, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 55237125, "ts": 1754353913752086, "dur": 566, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 55237125, "ts": 1754353913744876, "dur": 7812, "ph": "X", "name": "Write chrome-trace events", "args": {} },
