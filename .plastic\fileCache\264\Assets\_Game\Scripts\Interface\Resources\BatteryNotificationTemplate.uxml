<engine:UXML xmlns:engine="UnityEngine.UIElements" editor-extension-mode="False">
    <Style src="project://database/Assets/Resources/alert.uss?fileID=7433441132597879392&amp;guid=1ce1f1ca75e43684095bda95dc431517&amp;type=3#alert" />
    <engine:VisualElement name="BatteryNotificationContainer" class="battery-notification-container">
        <engine:VisualElement name="NotificationBatteryIndicator" class="notification-battery-indicator">
            <engine:VisualElement name="NotificationBatteryIcon" class="notification-battery-icon" />
            <engine:VisualElement name="NotificationBatteryBar" class="battery-bar notification-battery-bar">
                <!-- Segments will be created dynamically by the script -->
            </engine:VisualElement>
        </engine:VisualElement>
    </engine:VisualElement>
</engine:UXML>