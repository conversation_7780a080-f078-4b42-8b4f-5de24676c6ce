using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;
using System.Collections.Generic;

public class BatteryController : MonoBehaviour
{
    [SerializeField] private PlayerStatus playerStatus;

    // Preload the battery icons as assets instead of loading at runtime
    [Header("Battery Icons")]
    [SerializeField] private Texture2D emptyBatteryTexture;
    [SerializeField] private Texture2D lowBatteryTexture;
    [SerializeField] private Texture2D mediumBatteryTexture; 
    [SerializeField] private Texture2D fullBatteryTexture;

    [Header("Segment Settings")]
    [SerializeField, Range(10f, 500f)] private float energyPerSegment = 100f; // How much energy each segment represents
    
    private VisualElement batteryBar; // Container for segments
    private VisualElement batteryIcon; // The battery icon element
    private List<VisualElement> segments = new List<VisualElement>(); // Individual segment elements
    
    // Notification elements
    [Header("Notification Settings")]
    [SerializeField] private float blinkSpeed = 0.5f;
    
    private VisualElement activeNotification;
    private List<VisualElement> notificationSegments = new List<VisualElement>();
    private Coroutine blinkCoroutine;
    private List<int> depletedSegmentIndices = new List<int>();

    private UIDocument uiDocument;
    private bool isInitialized = false;
    private bool isUIVisible = false;

    [Header("Animation Settings")]
    [SerializeField, Range(50f, 300f)] private float fillAnimationSpeed = 50f; // Percentage points per second
    [SerializeField, Range(0.1f, 1f)] private float initialFillDelay = 0f; // Delay before starting the fill animation
    [SerializeField] private bool animateOnUIOpen = true; // Whether to animate when UI first opens
    [SerializeField] private bool skipAnimationOnFirstLaunch = true; // Skip animation on first UI open after game launch
    
    private float targetBatteryPercentage = 0f;
    private float currentBatteryPercentage = 0f;
    private float lastKnownEnergyPercentage = 0f; // Store last energy value even when UI is hidden
    private float animationSpeed = 100f; // Percentage points per second
    private Coroutine batteryAnimationCoroutine;
    private bool isFirstLaunch = true; // Track whether this is the first UI open since game launch
    
    // Track previous segment states for depletion detection
    private int previousFilledSegments = -1;
    private bool hasInitializedSegmentCount = false;

    // Initialize when the game starts
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
    private static void InitializeOnStartup()
    {
        // Ensure there's a battery controller in the scene
        EnsureBatteryController();
    }

    // Static method to ensure there's a battery controller in the scene
    public static BatteryController EnsureBatteryController()
    {
        // First try to find an existing controller
        var existingController = FindObjectOfType<BatteryController>();
        if (existingController != null)
        {
            return existingController;
        }   

        // Look for the InvUI component which should have a UIDocument
        var invUI = FindObjectOfType<InvUI>();
        if (invUI != null)
        {
            // Check if the InvUI GameObject already has a BatteryController
            var controller = invUI.GetComponent<BatteryController>();
            if (controller == null)
            {
                // If not, add one
                controller = invUI.gameObject.AddComponent<BatteryController>();
                Debug.Log("Added BatteryController to InvUI GameObject");
            }
            return controller;
        }

        // If no InvUI exists, create a new GameObject
        var newGameObject = new GameObject("BatteryController");
        var newController = newGameObject.AddComponent<BatteryController>();
        Debug.Log("Created new GameObject with BatteryController");
        return newController;
    }

    private void Awake()
    {
        // Try to get UIDocument from the same GameObject
        uiDocument = GetComponent<UIDocument>();
        
        // If not found, try to find InvUI and get its UIDocument
        if (uiDocument == null)
        {
            var invUI = GetComponent<InvUI>();
            if (invUI != null)
            {
                uiDocument = invUI.GetComponent<UIDocument>();
            }
        }
        
        // Load textures if not already assigned in inspector
        LoadBatteryTextures();
        
        // Use the serialized animation speed
        animationSpeed = fillAnimationSpeed;
    }

    private void LoadBatteryTextures()
    {
        // Only load if not already assigned
        if (emptyBatteryTexture == null)
        {
            emptyBatteryTexture = Resources.Load<Texture2D>("UI/BatteryIcon_0");
            if (emptyBatteryTexture == null)
                Debug.LogError("Failed to load empty battery texture");
        }
        
        if (lowBatteryTexture == null)
        {
            lowBatteryTexture = Resources.Load<Texture2D>("UI/BatteryIcon_15");
            if (lowBatteryTexture == null)
                Debug.LogError("Failed to load low battery texture");
        }
        
        if (mediumBatteryTexture == null)
        {
            mediumBatteryTexture = Resources.Load<Texture2D>("UI/BatteryIcon_50");
            if (mediumBatteryTexture == null)
                Debug.LogError("Failed to load medium battery texture");
        }
        
        if (fullBatteryTexture == null)
        {
            fullBatteryTexture = Resources.Load<Texture2D>("UI/BatteryIcon_100");
            if (fullBatteryTexture == null)
                Debug.LogError("Failed to load full battery texture");
        }
    }

    private void Start()
    {
        // Find player status if not assigned
        if (playerStatus == null)
        {
            playerStatus = FindObjectOfType<PlayerStatus>();
        }

        if (playerStatus != null)
        {
            playerStatus.OnEnergyChanged += HandleEnergyChanged;
        }
        
        // Wait for UI document to be ready
        StartCoroutine(InitializeUIAfterDelay());
    }
    
    private IEnumerator InitializeUIAfterDelay()
    {
        // Wait a few frames to ensure UI document is fully loaded
        yield return new WaitForEndOfFrame();
        yield return new WaitForEndOfFrame();
        
        // Try to find UIDocument again if still null
        if (uiDocument == null)
        {
            uiDocument = GetComponent<UIDocument>();
            if (uiDocument == null)
            {
                Debug.LogWarning("BatteryController: UIDocument not found on this GameObject. Battery display will not work.");
                yield break;
            }
        }
        
        InitializeUI();
        
        // Set initial battery display
        if (playerStatus != null && isInitialized)
        {
            // Check if UI is visible
            bool isVisible = uiDocument.enabled;
            if (isVisible)
            {
                targetBatteryPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
                
                if (animateOnUIOpen)
                {
                    // Start with empty battery
                    currentBatteryPercentage = 0f;
                    UpdateBatteryDisplay(currentBatteryPercentage);
                    
                    // Animate to the current value
                    if (batteryAnimationCoroutine != null)
                        StopCoroutine(batteryAnimationCoroutine);
                    batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(initialFillDelay));
                }
                else
                {
                    // No animation, just set directly
                    currentBatteryPercentage = targetBatteryPercentage;
                    UpdateBatteryDisplay(currentBatteryPercentage);
                }
            }
        }
    }

    private void OnDestroy()
    {
        if (playerStatus != null)
        {
            playerStatus.OnEnergyChanged -= HandleEnergyChanged;
        }
        
        // Stop any running coroutines
        if (blinkCoroutine != null)
        {
            StopCoroutine(blinkCoroutine);
        }
    }

    private void InitializeUI()
    {
        if (uiDocument == null)
        {
            Debug.LogError("UIDocument is null! Can't find battery elements.");
            return;
        }

        var root = uiDocument.rootVisualElement;
        
        if (root == null)
        {
            Debug.LogError("Root visual element is null!");
            return;
        }

        // Initialize main battery display
        InitializeMainBatteryDisplay(root);

        if (isInitialized)
        {
            Debug.Log("Successfully initialized battery UI elements!");
            
            // Automatically detect if UI is visible based on UIDocument enabled state
            var energyContainer = root.Q<VisualElement>("EnergyContainer");
            SetUIVisibility(uiDocument.enabled && IsUIElementVisible(energyContainer));

            // Monitor for UI document enabled state changes
            StartCoroutine(MonitorUIVisibility());
        }
    }

    private void InitializeMainBatteryDisplay(VisualElement root)
    {
        // First find the EnergyContainer 
        var energyContainer = root.Q<VisualElement>("EnergyContainer");
        if (energyContainer == null)
        {
            Debug.LogWarning("EnergyContainer not found in UI hierarchy - battery display will not work.");
            return;
        }

        // Then the BatteryIndicator
        var batteryIndicator = energyContainer.Q<VisualElement>("BatteryIndicator");
        if (batteryIndicator == null)
        {
            Debug.LogWarning("BatteryIndicator not found in UI hierarchy - battery display will not work.");
            return;
        }

        // Get the battery icon
        batteryIcon = batteryIndicator.Q<VisualElement>("BatteryIcon");
        if (batteryIcon == null)
        {
            Debug.LogWarning("BatteryIcon not found in UI hierarchy - battery icon will not update.");
        }

        // Get the BatteryBar container
        batteryBar = batteryIndicator.Q<VisualElement>("BatteryBar");
        if (batteryBar == null)
        {
            Debug.LogWarning("BatteryBar not found in UI hierarchy - battery segments will not work.");
            return;
        }

        // Clear old fill elements and create segments
        batteryBar.Clear();
        segments.Clear();
        CreateSegments(batteryBar, segments);
        
        isInitialized = true;
    }

    private void CreateSegments(VisualElement container, List<VisualElement> segmentList)
    {
        if (playerStatus == null)
        {
            Debug.LogWarning("[BatteryController] PlayerStatus not available when creating segments");
            return;
        }
        
        // Calculate number of segments based on max energy and energy per segment
        int segmentCount = Mathf.CeilToInt(playerStatus.maxEnergy / energyPerSegment);
        
        Debug.Log($"[BatteryController] Creating {segmentCount} segments (maxEnergy: {playerStatus.maxEnergy}, energyPerSegment: {energyPerSegment})");
        
        // Add class to container for segment count specific styling
        container.AddToClassList($"segments-{segmentCount}");
        
        for (int i = 0; i < segmentCount; i++)
        {
            // Create container for the segment
            var segmentContainer = new VisualElement();
            segmentContainer.AddToClassList("battery-segment-container");
            
            // The actual fill element inside the container
            var segment = new VisualElement();
            segment.AddToClassList("battery-segment");
            segment.AddToClassList("battery-segment-fill");
            segment.style.width = Length.Percent(0); // Start empty
            
            // Add the fill to the container
            segmentContainer.Add(segment);
            
            // Add the container to the battery bar
            container.Add(segmentContainer);
            
            // Store reference to the fill element (not the container)
            segmentList.Add(segment);
        }
        
        // Ensure proper layout update
        container.MarkDirtyRepaint();
    }

    // Check if a UI element is actually visible in the hierarchy
    private bool IsUIElementVisible(VisualElement element)
    {
        if (element == null) return false;
        
        // Check if this element is visible
        if (element.style.display == DisplayStyle.None)
            return false;
            
        if (element.resolvedStyle.opacity <= 0.01f) // Effectively invisible
            return false;
            
        // An element with zero width or height is not visible
        if (element.resolvedStyle.width <= 0 || element.resolvedStyle.height <= 0)
            return false;
            
        return true;
    }

    // Monitor for UI panel visibility changes
    private IEnumerator MonitorUIVisibility()
    {
        bool previousState = isUIVisible;
        
        while (true)
        {
            // Check if UIDocument is enabled
            bool currentVisibility = false;
            
            if (uiDocument != null && uiDocument.enabled && uiDocument.rootVisualElement != null)
            {
                var energyContainer = uiDocument.rootVisualElement.Q<VisualElement>("EnergyContainer");
                currentVisibility = IsUIElementVisible(energyContainer);
            }
            
            // If visibility state changed
            if (currentVisibility != previousState)
            {
                SetUIVisibility(currentVisibility);
                previousState = currentVisibility;
            }
            
            // Check every 0.2 seconds to avoid unnecessary performance impact
            yield return new WaitForSeconds(0.2f);
        }
    }

    private void HandleEnergyChanged(float oldEnergy, float newEnergy)
    {
        // Always update the last known energy percentage, even if UI is not visible
        lastKnownEnergyPercentage = (newEnergy / playerStatus.maxEnergy) * 100f;
        
        // Check for segment depletion
        CheckForSegmentDepletion(oldEnergy, newEnergy);
        
        // Only update visual display if UI is visible
        if (!isUIVisible) return;
        
        Debug.Log($"Energy changed: {oldEnergy} -> {newEnergy}, percentage: {(newEnergy / playerStatus.maxEnergy) * 100f}%");
        
        // Keep current value (don't reset to 0)
        // Just update target for animation
        targetBatteryPercentage = lastKnownEnergyPercentage;
        
        // Start smooth animation from current to new value
        if (batteryAnimationCoroutine != null)
            StopCoroutine(batteryAnimationCoroutine);
        batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(0f));
    }

    private void CheckForSegmentDepletion(float oldEnergy, float newEnergy)
    {
        if (playerStatus == null || segments.Count == 0) return;
        
        // Calculate segment states
        float energyPerSegmentActual = playerStatus.maxEnergy / segments.Count;
        int oldFilledSegments = Mathf.CeilToInt(oldEnergy / energyPerSegmentActual);
        int newFilledSegments = Mathf.CeilToInt(newEnergy / energyPerSegmentActual);
        
        // First time initialization
        if (!hasInitializedSegmentCount)
        {
            previousFilledSegments = newFilledSegments;
            hasInitializedSegmentCount = true;
            return;
        }
        
        // Check if segments were depleted
        if (newFilledSegments < oldFilledSegments)
        {
            // Calculate which segments were depleted
            depletedSegmentIndices.Clear();
            for (int i = newFilledSegments; i < oldFilledSegments; i++)
            {
                depletedSegmentIndices.Add(i);
            }
            
            Debug.Log($"[BatteryController] {depletedSegmentIndices.Count} segments depleted! Segments {newFilledSegments} to {oldFilledSegments - 1}");
            ShowNotification();
        }
        
        previousFilledSegments = newFilledSegments;
    }

    private void ShowNotification()
    {
        if (NotificationManager.Instance == null)
        {
            Debug.LogWarning("[BatteryController] NotificationManager instance not found!");
            return;
        }

        // Calculate the state BEFORE depletion (what the battery looked like before losing segments)
        // We need to show the previous state so users can see what they lost
        float energyPerSegmentActual = playerStatus.maxEnergy / segments.Count;
        float previousEnergyState = previousFilledSegments * energyPerSegmentActual;
        float notificationPercentage = (previousEnergyState / playerStatus.maxEnergy) * 100f;

        Debug.Log($"[BatteryController] Showing notification with previous state: {notificationPercentage}% (was {previousFilledSegments} segments)");

        // Show battery notification
        activeNotification = NotificationManager.Instance.ShowBatteryNotification(
            notificationPercentage,
            (notification, segments) => {
                // This callback happens after the notification is properly in the UI
                OnNotificationInitialized(notification, segments);
            }
        );
    }

    private void OnNotificationInitialized(VisualElement notification, List<VisualElement> segments)
    {
        Debug.Log($"[BatteryController] OnNotificationInitialized called. Notification: {notification?.name}");

        // Get the battery bar and create segments
        var batteryBar = notification.Q<VisualElement>("BatteryBar");
        Debug.Log($"[BatteryController] BatteryBar found: {batteryBar != null}");
        if (batteryBar != null)
        {
            // Clear and create fresh segments
            batteryBar.Clear();
            notificationSegments.Clear();
            CreateSegments(batteryBar, notificationSegments);
            Debug.Log($"[BatteryController] Created {notificationSegments.Count} notification segments");
        }

        // Calculate the previous state (before depletion) to show in notification
        float energyPerSegmentActual = playerStatus.maxEnergy / notificationSegments.Count;
        float previousEnergyState = previousFilledSegments * energyPerSegmentActual;
        float previousPercentage = (previousEnergyState / playerStatus.maxEnergy) * 100f;

        // Update battery icon to show the previous state
        var batteryIcon = notification.Q<VisualElement>("BatteryIcon");
        Debug.Log($"[BatteryController] BatteryIcon found: {batteryIcon != null}");
        if (batteryIcon != null)
        {
            UpdateBatteryIcon(previousPercentage, batteryIcon);
        }

        // Show the segments as they were BEFORE depletion
        UpdateSegments(notificationSegments, previousPercentage);

        Debug.Log($"[BatteryController] Notification showing previous state: {previousPercentage}%, depleted segments: [{string.Join(", ", depletedSegmentIndices)}]");

        // Start blinking animation for depleted segments
        if (blinkCoroutine != null)
            StopCoroutine(blinkCoroutine);
        blinkCoroutine = StartCoroutine(BlinkDepletedSegments());
    }

    private IEnumerator BlinkDepletedSegments()
    {
        if (notificationSegments.Count == 0 || depletedSegmentIndices.Count == 0)
        {
            Debug.Log($"[BatteryController] Blinking cancelled: segments={notificationSegments.Count}, depleted={depletedSegmentIndices.Count}");
            yield break;
        }

        Debug.Log($"[BatteryController] Starting blink animation for {depletedSegmentIndices.Count} segments: [{string.Join(", ", depletedSegmentIndices)}]");

        bool showBlink = false;
        int blinkCount = 0;
        int maxBlinks = 10; // Blink for 5 seconds at 0.5s intervals

        while (activeNotification != null && activeNotification.parent != null && blinkCount < maxBlinks)
        {
            showBlink = !showBlink;
            blinkCount++;

            // Toggle the blink styling on depleted segment containers
            foreach (int segmentIndex in depletedSegmentIndices)
            {
                if (segmentIndex < notificationSegments.Count)
                {
                    var segment = notificationSegments[segmentIndex];
                    var container = segment.parent;
                    if (container != null)
                    {
                        if (showBlink)
                        {
                            // Apply blink styling directly
                            container.style.backgroundColor = new Color(1f, 0.3f, 0.3f, 0.9f); // Bright red
                            container.style.borderTopWidth = 1;
                            container.style.borderBottomWidth = 1;
                            container.style.borderLeftWidth = 1;
                            container.style.borderRightWidth = 1;
                            container.style.borderTopColor = new Color(1f, 0.2f, 0.2f, 1f);
                            container.style.borderBottomColor = new Color(1f, 0.2f, 0.2f, 1f);
                            container.style.borderLeftColor = new Color(1f, 0.2f, 0.2f, 1f);
                            container.style.borderRightColor = new Color(1f, 0.2f, 0.2f, 1f);
                            Debug.Log($"[BatteryController] Applied blink styling to segment {segmentIndex}");
                        }
                        else
                        {
                            // Remove blink styling - back to normal
                            container.style.backgroundColor = new Color(20f/255f, 20f/255f, 20f/255f, 1f); // Dark background
                            container.style.borderTopWidth = 0;
                            container.style.borderBottomWidth = 0;
                            container.style.borderLeftWidth = 0;
                            container.style.borderRightWidth = 0;
                            Debug.Log($"[BatteryController] Removed blink styling from segment {segmentIndex}");
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"[BatteryController] Container is null for segment {segmentIndex}");
                    }
                }
                else
                {
                    Debug.LogWarning($"[BatteryController] Segment index {segmentIndex} out of range (max: {notificationSegments.Count - 1})");
                }
            }

            yield return new WaitForSeconds(blinkSpeed);
        }

        Debug.Log($"[BatteryController] Blink animation finished after {blinkCount} blinks");

        // Clean up when done - remove blink classes
        foreach (int segmentIndex in depletedSegmentIndices)
        {
            if (segmentIndex < notificationSegments.Count)
            {
                var segment = notificationSegments[segmentIndex];
                var container = segment.parent;
                container?.RemoveFromClassList("battery-segment-blink");
            }
        }

        // Clean up references
        depletedSegmentIndices.Clear();
        notificationSegments.Clear();
        activeNotification = null;
    }

    // Check if UI is visible and needs updates
    public void SetUIVisibility(bool isVisible)
    {
        if (isUIVisible != isVisible)
        {
            isUIVisible = isVisible;
            
            // If becoming visible, update the display
            if (isUIVisible && playerStatus != null && isInitialized)
            {
                // Set the target percentage to current energy
                targetBatteryPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
                
                // For initial display, don't animate - just show current value
                currentBatteryPercentage = targetBatteryPercentage;
                UpdateBatteryDisplay(currentBatteryPercentage);
            }
        }
    }
    
    // Coroutine to animate the battery fill from previous to current value
    private IEnumerator AnimateBatteryFill(float delay)
    {
        // Optional delay before starting animation
        if (delay > 0)
            yield return new WaitForSeconds(delay);
        
        // Store the starting value
        float startValue = currentBatteryPercentage;
        
        // Animate until we reach target
        while (Mathf.Abs(currentBatteryPercentage - targetBatteryPercentage) > 0.1f)
        {
            currentBatteryPercentage = Mathf.MoveTowards(
                currentBatteryPercentage, 
                targetBatteryPercentage, 
                animationSpeed * Time.deltaTime
            );
            
            UpdateBatteryDisplay(currentBatteryPercentage);
            yield return null;
        }
        
        // Final update to ensure we hit the exact target
        currentBatteryPercentage = targetBatteryPercentage;
        UpdateBatteryDisplay(currentBatteryPercentage);
        
        batteryAnimationCoroutine = null;
    }

    // Update only when necessary
    private void Update()
    {
        // If UI is not visible, don't update
        if (!isUIVisible)
            return;

        // Initialize if needed
        if (!isInitialized && uiDocument != null && uiDocument.rootVisualElement != null)
        {
            InitializeUI();
            if (!isInitialized)
                return;
        }
         
        // Test controls for energy manipulation 
        if (Input.GetKeyDown(KeyCode.KeypadPlus) || Input.GetKeyDown(KeyCode.Equals))
        {
            playerStatus.RestoreEnergy(energyPerSegment);
            Debug.Log($"Added energy: {playerStatus.currentEnergy}/{playerStatus.maxEnergy}");
        }
        else if (Input.GetKeyDown(KeyCode.KeypadMinus) || Input.GetKeyDown(KeyCode.Minus))
        {
            // Reduce by exactly one segment worth for easier testing
            float reductionAmount = energyPerSegment;
            float oldEnergy = playerStatus.currentEnergy;
            playerStatus.currentEnergy = Mathf.Max(0f, playerStatus.currentEnergy - reductionAmount);
            
            Debug.Log($"Reduced energy by {reductionAmount}: {playerStatus.currentEnergy}/{playerStatus.maxEnergy}");
            
            // Manually trigger the energy changed event to ensure it fires
            playerStatus.UpdateEnergy(playerStatus.currentEnergy);
        }
        
        // Force reset the battery display
        if (Input.GetKeyDown(KeyCode.B))
        {
            Debug.Log("Forcing battery display reset");
            ResetBatteryDisplay();
        }
        
        // Test notification system
        if (Input.GetKeyDown(KeyCode.N))
        {
            Debug.Log("Manually triggering notification");
            // Simulate depleting the last filled segment
            if (previousFilledSegments > 0)
            {
                depletedSegmentIndices.Clear();
                depletedSegmentIndices.Add(previousFilledSegments - 1);
                ShowNotification();
            }
        }
    }

    private void ResetBatteryDisplay()
    {
        // Re-setup elements
        InitializeUI();
        
        // Update display with animation
        if (playerStatus != null)
        {
            // Set both current and target to current energy
            currentBatteryPercentage = targetBatteryPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
            
            // Update immediately without animation
            UpdateBatteryDisplay(currentBatteryPercentage);
            
            Debug.Log($"Battery display reset with energy: {targetBatteryPercentage}%");
        }
    }
    
    /// <summary>
    /// Sets the battery percentage and updates the display
    /// </summary>
    /// <param name="percentage">Battery percentage (0-100)</param>
    public void SetBatteryPercentage(float percentage)
    {
        if (!isInitialized || !isUIVisible)
            return;
            
        float batteryPercentage = Mathf.Clamp(percentage, 0f, 100f);
        // Set target for animation
        targetBatteryPercentage = batteryPercentage;
        
        // Start animation from current to new value
        if (batteryAnimationCoroutine != null)
            StopCoroutine(batteryAnimationCoroutine);
        batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(0f));
    }

    /// <summary>
    /// Updates the battery fill display based on current percentage
    /// </summary>
    private void UpdateBatteryDisplay(float percentage)
    {
        if (segments == null || segments.Count == 0)
        {
            // Don't log error every frame - this is expected if battery display isn't initialized
            return;
        }

        // Update battery icon based on percentage
        UpdateBatteryIcon(percentage);
        
        // Update segments
        UpdateSegments(segments, percentage);
    }

    private void UpdateSegments(List<VisualElement> segmentList, float percentage)
    {
        if (segmentList.Count == 0) return;
        
        // Calculate energy per segment based on actual segment count
        float energyPerSegmentActual = 100f / segmentList.Count;
        
        for (int i = 0; i < segmentList.Count; i++)
        {
            var segment = segmentList[i];
            
            // Calculate this segment's fill percentage
            float segmentStartPercentage = i * energyPerSegmentActual;
            float segmentEndPercentage = (i + 1) * energyPerSegmentActual;
            
            float segmentFillPercentage = 0f;
            
            if (percentage >= segmentEndPercentage)
            {
                // Segment is fully filled
                segmentFillPercentage = 100f;
            }
            else if (percentage > segmentStartPercentage)
            {
                // Segment is partially filled
                float segmentProgress = (percentage - segmentStartPercentage) / energyPerSegmentActual;
                segmentFillPercentage = segmentProgress * 100f;
            }
            // else segment is empty (0%)
            
            // Apply the fill
            segment.style.width = Length.Percent(segmentFillPercentage);
        }
    }
    
    /// <summary>
    /// Updates the battery icon based on the energy percentage
    /// </summary>
    private void UpdateBatteryIcon(float percentage, VisualElement iconElement = null)
    {
        var targetIcon = iconElement ?? batteryIcon;
        if (targetIcon == null)
        {
            // Battery icon might not exist, that's ok
            return;
        }
    
        // Choose the appropriate texture based on the percentage
        Texture2D iconTexture = null;
        
        if (percentage <= 0 && emptyBatteryTexture != null)
        {
            iconTexture = emptyBatteryTexture;
        }
        else if (percentage <= 15 && lowBatteryTexture != null)
        {
            iconTexture = lowBatteryTexture;
        }
        else if (percentage <= 50 && mediumBatteryTexture != null)
        {
            iconTexture = mediumBatteryTexture;
        }
        else if (fullBatteryTexture != null)
        {
            iconTexture = fullBatteryTexture;
        }
        
        // Apply the texture if it's valid
        if (iconTexture != null)
        {
            targetIcon.style.backgroundImage = iconTexture;
        }
        else
        {
            Debug.LogWarning($"No valid battery texture found for percentage: {percentage}");
        }
    }

    // Public method to notify this controller when inventory is opened via Tab
    public void OnInventoryOpened()
    {
        if (isInitialized && playerStatus != null)
        {
            // Calculate current energy percentage
            float currentEnergyPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
            
            // Update last known percentage
            lastKnownEnergyPercentage = currentEnergyPercentage;
            
            // Set the UI to visible first
            isUIVisible = true;
            
            // Get the percentage difference between current display and actual energy
            // If the battery has changed significantly while UI was closed, animate the change
            if (Mathf.Abs(currentBatteryPercentage - currentEnergyPercentage) > 5f && animateOnUIOpen)
            {
                // Skip animation on first launch if requested
                if (isFirstLaunch && skipAnimationOnFirstLaunch)
                {
                    // On first launch, just set values directly without animation
                    currentBatteryPercentage = currentEnergyPercentage;
                    targetBatteryPercentage = currentEnergyPercentage;
                    UpdateBatteryDisplay(currentBatteryPercentage);
                    isFirstLaunch = false;
                    return;
                }
                
                // If we haven't displayed anything yet or display is at 0
                if (currentBatteryPercentage <= 0.1f)
                {
                    // If we're opening for the first time, start at a non-zero value that's not the target
                    float startingPercentage = currentEnergyPercentage * 0.25f; // 25% of actual value
                    currentBatteryPercentage = Mathf.Max(5f, startingPercentage); // At least 5%
                }
                
                // Set target to actual energy
                targetBatteryPercentage = currentEnergyPercentage;
                
                // Start animation from current to new value with delay
                if (batteryAnimationCoroutine != null)
                    StopCoroutine(batteryAnimationCoroutine);
                batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(initialFillDelay));
                
                // Show initial state before animation
                UpdateBatteryDisplay(currentBatteryPercentage);
                
                // No longer first launch
                isFirstLaunch = false;
            }
            else
            {
                // No animation needed - just show current value
                currentBatteryPercentage = currentEnergyPercentage;
                targetBatteryPercentage = currentEnergyPercentage;
                UpdateBatteryDisplay(currentBatteryPercentage);
                isFirstLaunch = false;
            }
        }
    }
}