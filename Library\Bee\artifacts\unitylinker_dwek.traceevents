{ "pid": 238036, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 238036, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 238036, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 238036, "tid": 1, "ts": 1754353603277633, "dur": 380582, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 238036, "tid": 1, "ts": 1754353603279780, "dur": 50295, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 238036, "tid": 1, "ts": 1754353603286191, "dur": 42337, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 238036, "tid": 1, "ts": 1754353603353750, "dur": 12769, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 238036, "tid": 1, "ts": 1754353603367740, "dur": 68049, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 238036, "tid": 1, "ts": 1754353603435830, "dur": 19587, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 238036, "tid": 1, "ts": 1754353603455432, "dur": 192538, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 238036, "tid": 1, "ts": 1754353603655284, "dur": 2752, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 238036, "tid": 1, "ts": 1754353603658217, "dur": 406, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 238036, "tid": 1, "ts": 1754353603667728, "dur": 2335, "ph": "X", "name": "", "args": {} },
{ "pid": 238036, "tid": 1, "ts": 1754353603666849, "dur": 3597, "ph": "X", "name": "Write chrome-trace events", "args": {} },
