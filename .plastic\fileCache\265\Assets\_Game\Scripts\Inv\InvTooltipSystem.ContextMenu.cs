using UnityEngine;
using UnityEngine.UIElements;
using System;
using Inventory;

public partial class InvTooltipSystem
{
    // ---------------- Context-menu UI ----------------
    private void CreateContextMenuUI()
    {
        contextMenu = new VisualElement { name = "ContextMenu" };
        contextMenu.AddToClassList("context-menu-container");
        contextMenu.style.display    = DisplayStyle.None;
        contextMenu.style.visibility = Visibility.Hidden;
        contextMenu.style.position   = Position.Absolute;
        contextMenu.pickingMode      = PickingMode.Position;

        // Fallback styling - match tooltip black background
        contextMenu.style.backgroundColor  = new Color(0f, 0f, 0f, 0.9f);
        contextMenu.style.borderTopWidth   = 1;
        contextMenu.style.borderBottomWidth = 1;
        contextMenu.style.borderLeftWidth  = 1;
        contextMenu.style.borderRightWidth = 1;
        Color borderColor = new Color(0.3f, 0.3f, 0.3f, 1f);
        contextMenu.style.borderTopColor    = borderColor;
        contextMenu.style.borderBottomColor = borderColor;
        contextMenu.style.borderLeftColor   = borderColor;
        contextMenu.style.borderRightColor  = borderColor;
        contextMenu.style.minWidth  = 150;
        contextMenu.style.minHeight = 50;
        contextMenu.style.paddingTop    = 4;
        contextMenu.style.paddingBottom = 4;
        contextMenu.style.paddingLeft   = 4;
        contextMenu.style.paddingRight  = 4;

        root.Add(contextMenu);
        Debug.Log("Context menu UI created");
    }

    public void ShowContextMenu(string slotType, Vector2 mousePos)
    {
        if (slotHandler == null || contextMenu == null)
        {
            Debug.LogWarning("ShowContextMenu called before tooltip system is initialized");
            return;
        }

        var stack = slotHandler.GetStackFromSlot(slotType);
        if (stack == null || stack.Item == null) return;

        currentSlotType = slotType;
        currentStack    = stack;

        // Demo mode: don't allow naming
        if (!namingEnabled)
        {
            ShowRegularContextMenu(slotType, mousePos);
            return;
        }

        bool isUnnamed = !currentStack.Item.HasPlayerName;

        if (isUnnamed)
        {
            ShowNamingInTooltip(mousePos);
        }
        else
        {
            ShowRegularContextMenu(slotType, mousePos);
        }
    }

    private void ShowRegularContextMenu(string slotType, Vector2 mousePos)
    {
        Vector2 panelPosition = root.panel != null
            ? RuntimePanelUtils.ScreenToPanel(root.panel, mousePos)
            : mousePos;

        currentMousePos = panelPosition;
        HideTooltip();

        BuildContextMenuContent();

        // Show but keep invisible during initial layout to avoid flicker
        contextMenu.style.display    = DisplayStyle.Flex;
        contextMenu.style.visibility = Visibility.Hidden;
        justShowedContextMenu        = true;

        contextMenu.schedule.Execute(() =>
        {
            contextMenu.MarkDirtyRepaint();
            root.MarkDirtyRepaint();
        }).ExecuteLater(1);

        contextMenu.schedule.Execute(() =>
        {
            contextMenu.BringToFront();
            PositionContextMenu(panelPosition);

            // Now that position & size are settled, reveal the menu
            contextMenu.style.visibility = Visibility.Visible;
            justShowedContextMenu = false;
        }).ExecuteLater(50);

        isContextVisible = true;
    }

    // ---------- Build context-menu content ------------
    private void BuildContextMenuContent()
    {
        contextMenu.Clear();
        if (currentStack?.Item == null) return;

        bool isUnnamed = namingEnabled && !currentStack.Item.HasPlayerName;
        if (isUnnamed)
        {
            CreateNamingSection();
            var msg = new Label("Name this item to unlock actions");
            msg.AddToClassList("context-message");
            contextMenu.Add(msg);
            return;
        }

        CreateNameDisplaySection();
        CreateDescriptionDisplaySection();

        Type itemType         = currentStack.Item.GetType();
        bool addedSomeActions = false;

        // Exact type actions
        if (actionRegistry.TryGetValue(itemType, out var list))
        {
            foreach (var (name, action) in list)
            {
                if (name == "Split Stack" && currentStack.Quantity <= 1) continue;

                // For Manual items, dynamically determine the action name
                string displayName = name;
                if (itemType == typeof(Manual) && name == "Read")
                {
                    displayName = GetManualActionName(currentStack.Item as Manual);
                }

                AddContextMenuItem(displayName, () => action(currentStack, currentSlotType));
                addedSomeActions = true;
            }
        }

        // Base-type actions (but skip if more specific actions exist)
        var baseType = itemType.BaseType;
        while (baseType != null && baseType != typeof(Item))
        {
            if (actionRegistry.TryGetValue(baseType, out var blist))
            {
                foreach (var (name, action) in blist)
                {
                    if (name == "Split Stack" && currentStack.Quantity <= 1) continue;

                    // Skip base type actions if the exact type already has actions
                    // This prevents Manual items from showing both "Read" and "Use"
                    if (actionRegistry.ContainsKey(itemType) && actionRegistry[itemType].Count > 0)
                    {
                        // Skip "Use" action for Manual items since they have "Read"
                        if (name == "Use" && itemType == typeof(Manual))
                            continue;
                    }

                    AddContextMenuItem(name, () => action(currentStack, currentSlotType));
                    addedSomeActions = true;
                }
            }
            baseType = baseType.BaseType;
        }

        // Default actions (all items)
        if (actionRegistry.TryGetValue(typeof(Item), out var defaultActions))
        {
            if (addedSomeActions) AddContextMenuSeparator();
            foreach (var (name, action) in defaultActions)
            {
                if (name == "Split Stack" && currentStack.Quantity <= 1) continue;
                AddContextMenuItem(name, () => action(currentStack, currentSlotType));
            }
        }

        // Add editing actions for named items (only if naming is enabled)
        if (namingEnabled && !isUnnamed)
        {
            if (addedSomeActions || (actionRegistry.ContainsKey(typeof(Item)) && actionRegistry[typeof(Item)].Count > 0))
                AddContextMenuSeparator();

            AddContextMenuItem("Edit Name", () => { SwitchToNameEditing(); });
            AddContextMenuItem("Edit Description", () => { SwitchToDescriptionEditing(); });
        }
    }

    // ------- Naming section helpers (context-menu) ------
    private void CreateNamingSection()
    {
        var section = new VisualElement();
        section.AddToClassList("context-name-section");

        var nameFieldCM = new TextField { value = currentStack.Item.PlayerGivenName ?? string.Empty };
        nameFieldCM.AddToClassList("context-name-field");

        nameFieldCM.schedule.Execute(() =>
        {
            var input = nameFieldCM.Q(className: "unity-text-field__input");
            if (input != null)
            {
                input.style.backgroundColor = new Color(25f/255f, 25f/255f, 25f/255f, 1f);
                input.style.color           = Color.white;
                input.style.borderTopWidth  = 0;
                input.style.borderBottomWidth = 0;
                input.style.borderLeftWidth   = 0;
                input.style.borderRightWidth  = 0;
            }
        }).ExecuteLater(1);

        nameFieldCM.RegisterCallback<FocusInEvent>(OnFocusIn);
        nameFieldCM.RegisterCallback<FocusOutEvent>(_ => RestorePlayerInput());
        nameFieldCM.RegisterCallback<KeyDownEvent>(evt =>
        {
            if (evt.keyCode == KeyCode.Return || evt.keyCode == KeyCode.KeypadEnter)
            {
                OnContextNameChanged(nameFieldCM.value);
                nameFieldCM.Blur();
                evt.StopPropagation();
            }
            else if (evt.keyCode == KeyCode.Escape)
            {
                nameFieldCM.Blur();
                HideContextMenu();
                evt.StopPropagation();
            }
        });

        section.Add(nameFieldCM);

        var confirmBtn = new Button(() =>
        {
            OnContextNameChanged(nameFieldCM.value);
            HideContextMenu();
        })
        { text = "Confirm Name" };
        confirmBtn.AddToClassList("context-action-button");
        confirmBtn.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
        confirmBtn.style.color           = Color.white;
        confirmBtn.style.marginTop       = 2;
        confirmBtn.style.marginBottom    = 2;
        confirmBtn.style.paddingTop      = 5;
        confirmBtn.style.paddingBottom   = 5;
        confirmBtn.style.paddingLeft     = 10;
        confirmBtn.style.paddingRight    = 10;
        confirmBtn.style.minHeight       = 25;
        section.Add(confirmBtn);

        contextMenu.Add(section);

        nameFieldCM.schedule.Execute(nameFieldCM.Focus).ExecuteLater(50);
    }

    private void CreateNameDisplaySection()
    {
        var label = new Label(currentStack.Item.PlayerGivenName);
        label.AddToClassList("context-name-display");
        label.style.backgroundColor = new Color(0.15f, 0.15f, 0.15f, 1f);
        label.style.color           = new Color(0.8f, 0.8f, 1f, 1f);
        label.style.borderTopWidth  = 1;
        label.style.borderBottomWidth = 1;
        label.style.borderLeftWidth  = 1;
        label.style.borderRightWidth = 1;
        Color borderColor = new Color(0.3f, 0.3f, 0.3f, 1f);
        label.style.borderTopColor    = borderColor;
        label.style.borderBottomColor = borderColor;
        label.style.borderLeftColor   = borderColor;
        label.style.borderRightColor  = borderColor;
        label.style.marginTop    = 2;
        label.style.marginBottom = 5;
        label.style.paddingTop   = 5;
        label.style.paddingBottom = 5;
        label.style.paddingLeft  = 10;
        label.style.paddingRight = 10;
        label.style.minHeight    = 25;

        label.RegisterCallback<ClickEvent>(_ => { SwitchToNameEditing(); });
        contextMenu.Add(label);
    }

    private void SwitchToNameEditing()
    {
        var nameDisp = contextMenu.Q<Label>(className: "context-name-display");
        if (nameDisp != null) contextMenu.Remove(nameDisp);
        CreateNamingSection();
        contextMenu.schedule.Execute(() => PositionContextMenu(currentMousePos)).ExecuteLater(1);
    }

    private void CreateDescriptionDisplaySection()
    {
        string effectiveDescription = currentStack.Item.GetEffectiveDescription();
        string displayText = string.IsNullOrEmpty(effectiveDescription) ? "Click to add description" : effectiveDescription;

        var label = new Label(displayText);
        label.AddToClassList("context-description-display");
        label.style.backgroundColor = new Color(0.1f, 0.1f, 0.1f, 1f);
        label.style.color = string.IsNullOrEmpty(effectiveDescription)
            ? new Color(0.5f, 0.5f, 0.5f, 1f)
            : new Color(0.7f, 0.7f, 0.7f, 1f);
        label.style.borderTopWidth  = 1;
        label.style.borderBottomWidth = 1;
        label.style.borderLeftWidth  = 1;
        label.style.borderRightWidth = 1;
        Color borderColor = new Color(0.3f, 0.3f, 0.3f, 1f);
        label.style.borderTopColor    = borderColor;
        label.style.borderBottomColor = borderColor;
        label.style.borderLeftColor   = borderColor;
        label.style.borderRightColor  = borderColor;
        label.style.marginTop    = 2;
        label.style.marginBottom = 5;
        label.style.paddingTop   = 5;
        label.style.paddingBottom = 5;
        label.style.paddingLeft  = 10;
        label.style.paddingRight = 10;
        label.style.minHeight    = 25;
        label.style.whiteSpace   = WhiteSpace.Normal;
        label.style.fontSize     = 10;

        label.RegisterCallback<ClickEvent>(_ => { SwitchToDescriptionEditing(); });
        contextMenu.Add(label);
    }

    private void SwitchToDescriptionEditing()
    {
        var descDisp = contextMenu.Q<Label>(className: "context-description-display");
        if (descDisp != null) contextMenu.Remove(descDisp);
        CreateDescriptionEditingSection();
        contextMenu.schedule.Execute(() => PositionContextMenu(currentMousePos)).ExecuteLater(1);
    }

    private void CreateDescriptionEditingSection()
    {
        var section = new VisualElement();
        section.AddToClassList("context-description-section");

        descriptionField = new TextField { value = currentStack.Item.PlayerGivenDescription ?? string.Empty };
        descriptionField.AddToClassList("context-description-field");
        descriptionField.multiline = true;
        descriptionField.style.height = 60; // Make it taller for descriptions

        descriptionField.schedule.Execute(() =>
        {
            var input = descriptionField.Q(className: "unity-text-field__input");
            if (input != null)
            {
                input.style.backgroundColor = new Color(25f/255f, 25f/255f, 25f/255f, 1f);
                input.style.color           = Color.white;
                input.style.borderTopWidth  = 0;
                input.style.borderBottomWidth = 0;
                input.style.borderLeftWidth   = 0;
                input.style.borderRightWidth  = 0;
                input.style.whiteSpace = WhiteSpace.Normal;
            }
        }).ExecuteLater(1);

        descriptionField.RegisterCallback<FocusInEvent>(OnFocusIn);
        descriptionField.RegisterCallback<FocusOutEvent>(_ => RestorePlayerInput());
        descriptionField.RegisterCallback<KeyDownEvent>(evt =>
        {
            if (evt.keyCode == KeyCode.Return && evt.ctrlKey) // Ctrl+Enter to confirm
            {
                OnContextDescriptionChanged(descriptionField.value);
                descriptionField.Blur();
                evt.StopPropagation();
            }
            else if (evt.keyCode == KeyCode.Escape)
            {
                descriptionField.Blur();
                HideContextMenu();
                evt.StopPropagation();
            }
        });

        section.Add(descriptionField);

        var confirmBtn = new Button(() =>
        {
            OnContextDescriptionChanged(descriptionField.value);
            HideContextMenu();
        })
        { text = "Confirm Description" };
        confirmBtn.AddToClassList("context-action-button");
        confirmBtn.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
        confirmBtn.style.color           = Color.white;
        confirmBtn.style.marginTop       = 2;
        confirmBtn.style.marginBottom    = 2;
        confirmBtn.style.paddingTop      = 5;
        confirmBtn.style.paddingBottom   = 5;
        confirmBtn.style.paddingLeft     = 10;
        confirmBtn.style.paddingRight    = 10;
        confirmBtn.style.minHeight       = 25;
        section.Add(confirmBtn);

        contextMenu.Add(section);

        descriptionField.schedule.Execute(descriptionField.Focus).ExecuteLater(50);
    }

    private void OnContextDescriptionChanged(string newDescription)
    {
        if (currentStack == null) return;
        currentStack.Item.PlayerGivenDescription = newDescription;
        Item.SavePlayerNames();
        BuildContextMenuContent();
        contextMenu.schedule.Execute(() => PositionContextMenu(currentMousePos)).ExecuteLater(1);
    }

    // -------------- Context-menu item helpers -------------
    private void AddContextMenuItem(string text, Action action)
    {
        var btn = new Button(action) { text = text };
        btn.AddToClassList("context-action-button");
        btn.style.unityTextAlign   = TextAnchor.MiddleLeft;
        btn.style.paddingLeft      = 16;
        btn.style.paddingRight     = 16;
        btn.style.paddingTop       = 8;
        btn.style.paddingBottom    = 8;
        btn.style.fontSize         = 13;
        btn.style.color            = new Color(159f/255f, 159f/255f, 159f/255f, 1f);
        btn.style.backgroundColor  = StyleKeyword.Null;

        btn.RegisterCallback<MouseEnterEvent>(_ =>
        {
            btn.style.backgroundColor = new Color(33f/255f, 33f/255f, 33f/255f, 0.5f);
            btn.style.color           = Color.white;
        });
        btn.RegisterCallback<MouseLeaveEvent>(_ =>
        {
            btn.style.backgroundColor = StyleKeyword.Null;
            btn.style.color           = new Color(159f/255f, 159f/255f, 159f/255f, 1f);
        });
        contextMenu.Add(btn);
    }

    private void AddContextMenuSeparator()
    {
        var sep = new VisualElement();
        sep.AddToClassList("separator");
        contextMenu.Add(sep);
    }

    // -------------- Position & visibility helpers --------
    private void PositionContextMenu(Vector2 mousePos)
    {
        Vector2 pos = mousePos;
        float  w = contextMenu.layout.width  > 0 ? contextMenu.layout.width  : 200f;
        float  h = contextMenu.layout.height > 0 ? contextMenu.layout.height : 100f;

        if (pos.x + w > root.layout.width)  pos.x = mousePos.x - w;
        if (pos.x < 0) pos.x = 0;
        if (pos.y + h > root.layout.height) pos.y = mousePos.y - h;
        if (pos.y < 0) pos.y = 0;

        contextMenu.style.left = pos.x;
        contextMenu.style.top  = pos.y;
    }

    private void HideContextMenu()
    {
        if (contextMenu != null)
        {
            contextMenu.style.display    = DisplayStyle.None;
            contextMenu.style.visibility = Visibility.Hidden;
        }
        isContextVisible = false;
        RestorePlayerInput();
        EnsureCursorState();
    }

    // Global click handling registered in Start (Core partial)
    private void OnGlobalMouseDown(MouseDownEvent evt)
    {
        if (!isContextVisible || justShowedContextMenu) return;
        Vector2 mousePos = evt.localMousePosition;
        if (!contextMenu.ContainsPoint(mousePos)) HideContextMenu();
    }

    // -------------- Naming via context-menu -------------
    private void OnContextNameChanged(string newName)
    {
        if (currentStack == null) return;
        currentStack.Item.PlayerGivenName = newName;
        Item.SavePlayerNames();
        BuildContextMenuContent();
        contextMenu.schedule.Execute(() => PositionContextMenu(currentMousePos)).ExecuteLater(1);
    }

    // ----------------- Action-registry API ---------------
    public void RegisterContextAction<T>(string actionName, Action<ItemStack, string> action) where T : Item
    {
        Type type = typeof(T);
        if (!actionRegistry.ContainsKey(type)) actionRegistry[type] = new();
        actionRegistry[type].Add((actionName, action));
    }

    // Replace the RegisterDefaultActions method in InvTooltipSystem.ContextMenu.cs

    private void RegisterDefaultActions()
    {
        // Drop (all items)
        RegisterContextAction<Item>("Drop", (stack, slotType) =>
        {
            var dragMan = FindObjectOfType<InvDragAndDropManager>();
            if (dragMan != null)
            {
                var method = typeof(InvDragAndDropManager).GetMethod("HandleItemDrop", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                method?.Invoke(dragMan, new object[] { slotType, Input.mousePosition });
            }
            HideContextMenu();
        });

        // Split (stackable)
        RegisterContextAction<Item>("Split Stack", (stack, slotType) =>
        {
            var splitter = FindObjectOfType<InvItemSplitter>();
            splitter?.ShowSplitPanel(slotType, stack);
            HideContextMenu();
        });

        // Manual-specific action - dynamically named based on content
        RegisterContextAction<Manual>("Read", (stack, slotType) =>
        {
            var manual = stack.Item as Manual;
            var playerStatus = equipmentManager.GetComponent<PlayerStatus>();
            if (playerStatus != null && manual != null)
            {
                // Use the manual
                manual.Use(playerStatus);

                // Remove the consumed manual
                slotHandler.RemoveStackFromSlot(slotType);
                invUI.UpdateUI();
            }
            HideContextMenu();
        });

        // Consumables – Use (but exclude Manual items since they have their own "Read" action)
        RegisterContextAction<ConsumableDefinition>("Use", (stack, slotType) =>
        {
            // Skip Manual items - they should only show "Read"
            if (stack.Item is Manual) return;

            var consumable   = stack.Item as ConsumableDefinition;
            var playerStatus = equipmentManager.GetComponent<PlayerStatus>();
            if (playerStatus != null && consumable != null)
            {
                // Use the virtual Use method
                consumable.Use(playerStatus);

                // Remove the consumed item
                slotHandler.RemoveStackFromSlot(slotType);
                invUI.UpdateUI();
            }
            HideContextMenu();
        });

        // Armor – Inspect
        RegisterContextAction<Armor>("Inspect", (stack, _) =>
        {
            var armor = stack.Item as Armor;
            if (armor != null)
            {
                Debug.Log($"Inspecting {armor.PlayerGivenName ?? armor.itemName}: Armor {armor.ArmorValue}, Extra HP {armor.ExtraHitPoints}, Durability {armor.Durability}");
            }
            HideContextMenu();
        });

        // Helmet – Inspect
        RegisterContextAction<Helmet>("Inspect", (stack, _) =>
        {
            var helmet = stack.Item as Helmet;
            if (helmet != null)
            {
                Debug.Log($"Inspecting {helmet.PlayerGivenName ?? helmet.itemName}: Armor {helmet.ArmorValue}, Durability {helmet.Durability}");
            }
            HideContextMenu();
        });

        // Bag – View storage
        RegisterContextAction<Bag>("View Storage", (stack, _) =>
        {
            var bag = stack.Item as Bag;
            if (bag != null)
            {
                Debug.Log($"Viewing storage for {bag.PlayerGivenName ?? bag.itemName}: {bag.GridWidth}x{bag.GridHeight} ({bag.StorageCapacity} slots)");
            }
            HideContextMenu();
        });
    }

    // Helper method to determine the appropriate action name for Manual items
    private string GetManualActionName(Manual manual)
    {
        if (manual == null || manual.itemInformationList == null || manual.itemInformationList.Count == 0)
            return "Read";

        int alreadyKnownCount = 0;
        int totalItems = 0;

        foreach (var info in manual.itemInformationList)
        {
            if (string.IsNullOrEmpty(info.targetItemName)) continue;

            Item targetItem = ItemDatabase.GetItemByName(info.targetItemName);
            if (targetItem == null) continue;

            totalItems++;

            // Check if item already has player-given name or description
            bool hasName = !string.IsNullOrEmpty(targetItem.PlayerGivenName);
            bool hasDescription = !string.IsNullOrEmpty(targetItem.PlayerGivenDescription);

            if (hasName || hasDescription)
            {
                alreadyKnownCount++;
            }
        }

        // Determine action name based on what's already known
        if (alreadyKnownCount > 0 && alreadyKnownCount == totalItems)
        {
            return "Overwrite Codex";
        }
        else if (alreadyKnownCount > 0)
        {
            return "Update Codex";
        }
        else
        {
            return "Read";
        }
    }
}