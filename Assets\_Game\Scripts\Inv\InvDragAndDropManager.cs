using UnityEngine;
using UnityEngine.UIElements;
using System;
using System.Collections.Generic;
using Inventory;
using System.Linq;

public class InvDragAndDropManager : MonoBehaviour
{
    [SerializeField] private InvItemDropping itemDropping;
    [SerializeField] private UIPanelManager uiPanelManager;
    [SerializeField] private StashSystem stashSystem;

    private VisualElement root;
    private EquipmentManager equipmentManager;
    private InvShiftClickHandler shiftClickHandler;
    private InvItemSplitter itemSplitter;
    private InvSlotHandler slotHandler;
    private InvItemMover itemMover;
    private InvUI invUI;
    private UIDocument invDocument;
    private VisualElement invRoot;
    private DraggedItemVisual draggedItemVisual;
    private InvTooltipSystem tooltipSystem;

    private const string CONTAINER_SLOT_PREFIX = "ContainerSlot";
    private const string STASH_SLOT_PREFIX = "StashSlot";
    private const string SELL_SLOT_PREFIX = "SellSlot";

    private float lastClickTime;
    private string lastClickedSlot;
    private const float doubleClickTime = 0.3f;

    private Vector2 mouseDownPosition;
    private Vector2 lastMousePosition;
    private const float dragThreshold = 5f;

    private HashSet<string> hoveredSlots = new HashSet<string>();
    private VisualElement currentHighlightedSlot;
    private VisualElement originalSlot;
    private VisualElement hoveredSlot;
    private VisualElement lastPreviewTarget;

    private List<VisualElement> previewElements = new List<VisualElement>();
    private bool isDraggingItem = false;
    public bool IsDraggingItem => isDraggingItem;

    // Add tracking for items being dragged
    private HashSet<ItemStack> itemsBeingDragged = new HashSet<ItemStack>();

    private Vector2Int sourceItemPosition;
    private string sourceItemContainerId;
    private bool isRotatedAtSource;
    private HashSet<Vector2Int> sourceItemOccupiedSlots = new HashSet<Vector2Int>();

    // Add this at the top of the class with the other private fields
    private HashSet<string> persistentHoverSlots = new HashSet<string>();
    private float lastHoverTime;
    private const float HOVER_DEBOUNCE_TIME = 0.02f; // Reduced debounce time for more responsive hovering

    // Add method to check if an item is being dragged
    public bool IsItemBeingDragged(ItemStack stack)
    {
        return itemsBeingDragged.Contains(stack);
    }

    private void Start()
    {
        InitializeComponents();
    }

    private void InitializeComponents()
    {
        equipmentManager = FindObjectOfType<EquipmentManager>();
        invUI = FindObjectOfType<InvUI>();
        root = GetComponent<UIDocument>()?.rootVisualElement;

        // Check for itemDropping and attempt to find it if null
        if (itemDropping == null)
        {
            Debug.LogWarning("InvItemDropping reference is missing. Attempting to find it in the scene.");
            itemDropping = FindObjectOfType<InvItemDropping>();
            if (itemDropping == null)
            {
                Debug.LogError("Failed to find InvItemDropping component. Item dropping will not work!");
            }
        }

        if (equipmentManager == null || invUI == null || root == null)
        {
            Debug.LogError("Required components missing in InvDragAndDropManager.");
            enabled = false;
            return;
        }

        slotHandler = new InvSlotHandler(equipmentManager, invUI);
        itemMover = new InvItemMover(equipmentManager, invUI);
        draggedItemVisual = new DraggedItemVisual(root, this);
        itemSplitter = FindObjectOfType<InvItemSplitter>();
        shiftClickHandler = new InvShiftClickHandler(equipmentManager, invUI, slotHandler, uiPanelManager, root);

        if (invUI != null)
        {
            invDocument = invUI.GetComponent<UIDocument>();
            invRoot = invDocument.rootVisualElement;
        }

        tooltipSystem = FindObjectOfType<InvTooltipSystem>();
        if (tooltipSystem == null)
        {
            // Create tooltip system if it doesn't exist
            var tooltipGO = new GameObject("TooltipSystem");
            tooltipSystem = tooltipGO.AddComponent<InvTooltipSystem>();
        }

        RegisterGlobalEvents();
    }

    private void RegisterGlobalEvents()
    {
        root.RegisterCallback<MouseUpEvent>(OnGlobalMouseUp);
        root.RegisterCallback<MouseMoveEvent>(OnGlobalMouseMove, TrickleDown.TrickleDown);
        root.focusable = true;
        root.RegisterCallback<KeyDownEvent>(OnGlobalKeyDown, TrickleDown.NoTrickleDown);
        root.Focus();
    }

    public void RegisterSlotForDragging(VisualElement slot, string slotType)
    {
        if (slot == null || string.IsNullOrEmpty(slotType))
        {
            Debug.LogWarning("Invalid slot or slotType for dragging registration.");
            return;
        }
        slot.userData = slotType;
        slot.RegisterCallback<MouseDownEvent>(OnSlotMouseDown);

        // Re-enable mouse enter/leave event handlers for tooltip
        slot.RegisterCallback<MouseEnterEvent>(OnSlotMouseEnter);
        slot.RegisterCallback<MouseLeaveEvent>(OnSlotMouseLeave);

        slot.AddToClassList("inventory-slot");
        slot.AddToClassList("draggable-slot");
    }

    private void OnSlotMouseEnter(MouseEnterEvent evt)
    {
        hoveredSlot = evt.currentTarget as VisualElement;
        if (hoveredSlot != null && hoveredSlot.userData is string slotType)
        {
            // Don't process if we're already hovering this slot
            if (persistentHoverSlots.Contains(slotType))
            {
                return;
            }

            // Check if we should process this hover event - debounce rapid hover changes
            bool shouldProcessHover = (Time.time - lastHoverTime) > HOVER_DEBOUNCE_TIME;
            if (!shouldProcessHover)
            {
                return;
            }

            lastHoverTime = Time.time;

            if (!draggedItemVisual.IsDragging)
            {
                var stack = slotHandler.GetStackFromSlot(slotType);
                if (stack != null && stack.Item != null)
                {
                    hoveredSlots.Add(slotType);
                    persistentHoverSlots.Add(slotType);

                    UpdateSlotVisualFeedback(hoveredSlot, true);

                    // Show tooltip
                    if (tooltipSystem != null)
                    {
                        Vector2 mousePos = Input.mousePosition;
                        mousePos.y = Screen.height - mousePos.y; // Convert to UI coordinates
                        tooltipSystem.ShowTooltip(slotType, mousePos, hoveredSlot);
                    }
                }
            }
        }
    }

    private void OnSlotMouseLeave(MouseLeaveEvent evt)
    {
        var slot = evt.currentTarget as VisualElement;
        if (slot != null && slot.userData is string slotType)
        {
            hoveredSlots.Remove(slotType);
            persistentHoverSlots.Remove(slotType); // Always remove from persistent registry on leave

            if (!draggedItemVisual.IsDragging)
            {
                UpdateSlotVisualFeedback(slot, false);

                // Hide tooltip
                if (tooltipSystem != null)
                {
                    tooltipSystem.HideTooltip();
                }
            }
        }
        if (slot == hoveredSlot)
        {
            hoveredSlot = null;
        }
    }

    private void UpdateSlotVisualFeedback(VisualElement slot, bool isHovered)
    {
        if (slot == null)
        {
            return;
        }

        var slotType = slot.userData as string;
        if (string.IsNullOrEmpty(slotType))
        {
            return;
        }

        // Get the stack at this slot - only apply hover to slots with items
        var stack = slotHandler.GetStackFromSlot(slotType);
        bool hasItem = stack != null && stack.Item != null;

        // Add or remove the hover class based on state and item presence
        if (isHovered && hasItem)
        {
            // Add hover effect for slots that have items
            slot.AddToClassList("slot-hovered");

            // Show battery preview for consumable items
            ShowBatteryPreviewForItem(stack.Item);
        }
        else
        {
            // Remove hover effects on mouse leave or for empty slots
            if (slot.ClassListContains("slot-hovered"))
            {
                slot.RemoveFromClassList("slot-hovered");
            }
            if (slot.ClassListContains("slot-dragging-over"))
            {
                slot.RemoveFromClassList("slot-dragging-over");
            }

            // Hide battery preview when not hovering
            HideBatteryPreview();
        }
    }

    private void OnGlobalMouseMove(MouseMoveEvent evt)
    {
        if (!invUI.IsInventoryVisible() || !draggedItemVisual.IsDragging) return;
        lastMousePosition = evt.mousePosition;
        draggedItemVisual.UpdatePosition(evt.mousePosition);

        // Check if we're outside any UI element
        bool isOutsideUI = !IsPointerInsideInventoryUI(evt.mousePosition);

        // Update the dropping label display
        if (isOutsideUI != draggedItemVisual.IsDraggingOutside)
        {
            draggedItemVisual.IsDraggingOutside = isOutsideUI;
            draggedItemVisual.UpdateDroppingLabel();
        }

        UpdateDropPreview(evt.mousePosition);
        UpdatePreview(evt.mousePosition);
    }

    // Helper method to check if pointer is inside any inventory UI element
    private bool IsPointerInsideInventoryUI(Vector2 position)
    {
        // First, check each specific container individually to determine if inside a container
        var invContainer = root.Q<VisualElement>("InvContainer");
        var stashContainer = root.Q<VisualElement>("StashContainer");
        var shopContainer = root.Q<VisualElement>("ShopContainer");
        var sellInterface = root.Q<VisualElement>("SellInterface");

        if (invContainer != null && invContainer.worldBound.Contains(position))
        {
            return true;
        }
        if (stashContainer != null && stashContainer.worldBound.Contains(position))
        {
            return true;
        }
        if (shopContainer != null && shopContainer.worldBound.Contains(position))
        {
            return true;
        }
        if (sellInterface != null && sellInterface.worldBound.Contains(position))
        {
            return true;
        }

        // Check all MainContainer elements
        var mainContainers = root.Query<VisualElement>("MainContainer").ToList();
        foreach (var container in mainContainers)
        {
            if (container != null && container.worldBound.Contains(position))
            {
                return true;
            }
        }

        // Additional UI elements to check
        var equipSlots = root.Q<VisualElement>("EquipmentSlots");
        if (equipSlots != null && equipSlots.worldBound.Contains(position))
        {
            return true;
        }

        // Check for any other interactive UI elements
        var buttons = root.Query<Button>().ToList();
        foreach (var button in buttons)
        {
            if (button.worldBound.Contains(position))
            {
                return true;
            }
        }

        // If we reach here, we're outside all UI elements
        return false;
    }

    private void UpdatePreview(Vector2 mousePosition)
    {
        if (!draggedItemVisual?.IsDragging ?? true) return;
        ClearPreviewElements();
        VisualElement targetElement = GetElementAtPosition(mousePosition);
        string targetSlotType = GetSlotType(targetElement);

        UpdateEquipmentSlotHighlights(draggedItemVisual.DraggedItemStack?.Item);

        // Check if we're outside any UI element
        bool isOutsideUI = !IsPointerInsideInventoryUI(mousePosition);

        // Update the dropping label display
        if (isOutsideUI != draggedItemVisual.IsDraggingOutside)
        {
            draggedItemVisual.IsDraggingOutside = isOutsideUI;
            draggedItemVisual.UpdateDroppingLabel();
        }

        if (targetElement != null && !string.IsNullOrEmpty(targetSlotType))
        {
            var (targetCategory, targetIdentifier, targetPosition) = ParseSlotType(targetSlotType);
            Item draggedItem = slotHandler.GetStackFromSlot(draggedItemVisual.SourceSlotType)?.Item;
            if (draggedItem != null)
            {
                // Special validation for bags
                if (draggedItem is Bag)
                {
                    var (sourceCategory, sourceIdentifier, _) = ParseSlotType(draggedItemVisual.SourceSlotType);

                    // Check if we're trying to drag an equipped bag to stash/sell
                    bool isEquippedBag = Enum.TryParse(draggedItemVisual.SourceSlotType, out EquipmentSlotType sourceSlotTypeEnum) &&
                                        sourceSlotTypeEnum == ((EquipmentBase)draggedItem).Slot;

                    // Don't allow dropping bag into its own container
                    if (isEquippedBag && targetCategory == "ContainerSlot" && targetIdentifier == ((EquipmentBase)draggedItem).Slot.ToString())
                    {
                        Debug.Log("Cannot drop a bag into its own container");
                        ShowInvalidOperationPreview(targetElement);
                        return;
                    }

                    // Check if we're trying to move a bag with items to stash/sell
                    if (isEquippedBag && (targetCategory == "StashSlot" || targetCategory == "SellSlot"))
                    {
                        // Get bag content to see if it has items
                        var bagSlot = equipmentManager.GetEquipmentSlot(((EquipmentBase)draggedItem).Slot);
                        if (bagSlot?.storageContainer != null)
                        {
                            var items = bagSlot.storageContainer.GetItems();
                            if (items != null && items.Any())
                            {
                                Debug.Log("Cannot move bag with items to stash/sell");
                                ShowInvalidOperationPreview(targetElement);
                                return;
                            }
                        }
                    }
                }

                InvItemContainer container = null;
                if (targetCategory == "ContainerSlot" && Enum.TryParse(targetIdentifier, out EquipmentSlotType eqType))
                {
                    var slot = equipmentManager.GetEquipmentSlot(eqType);
                    container = slot?.storageContainer;
                }
                else if (targetCategory == "StashSlot" || targetCategory == "SellSlot")
                {
                    var stashSystem = equipmentManager.GetComponent<StashSystem>();
                    container = targetCategory == "StashSlot"
                        ? stashSystem.GetStashContainer()
                        : stashSystem.GetSellContainer();
                }

                if (container != null)
                {
                    // Get the dimensions of the item being dragged
                    int w = draggedItemVisual.IsRotated ? draggedItem.SlotDimension.Height : draggedItem.SlotDimension.Width;
                    int h = draggedItemVisual.IsRotated ? draggedItem.SlotDimension.Width : draggedItem.SlotDimension.Height;

                    var targetStack = container.GetStackAtPosition(targetPosition);
                    bool canPlaceStorage = true;

                    // Special handling for bags
                    if (draggedItem is Bag)
                    {
                        // Check if we're moving a bag that's already in a container
                        var sourceSlotInfo = ParseSlotType(draggedItemVisual.SourceSlotType);
                        bool isBagInContainer = sourceSlotInfo.category == "ContainerSlot";

                        if (isBagInContainer)
                        {
                            // Bags in containers can be moved normally
                            canPlaceStorage = true;
                        }
                        else
                        {
                            // Otherwise use the normal check for equipped bags
                            canPlaceStorage = CanPlaceStorageEquipment(draggedItem);
                        }
                    }
                    else
                    {
                        canPlaceStorage = CanPlaceStorageEquipment(draggedItem);
                    }

                    if (!canPlaceStorage)
                    {
                        ShowPreviewCells(container, targetPosition, w, h, false, false, targetElement);
                        return;
                    }

                    // Check if target slots overlap with original slots
                    bool overlapsWithOriginal = CheckIfOverlapsWithOriginalPosition(targetPosition, w, h, targetIdentifier);

                    // Placement is valid if:
                    // 1. Target is empty and can place item OR
                    // 2. Overlaps with the item's original position (same container)
                    bool canPlace = (targetStack == null && container.CanPlaceItem(draggedItem, targetPosition, draggedItemVisual.IsRotated)) ||
                                  (targetIdentifier == sourceItemContainerId && overlapsWithOriginal);

                    bool isStackCombination = targetStack != null &&
                                              targetStack.Item == draggedItem &&
                                              !draggedItem.IsMultiSlot &&
                                              targetStack.Quantity < targetStack.Item.maxStack;

                    ShowPreviewCells(container, targetPosition, w, h, canPlace || isStackCombination, false, targetElement);
                }
            }
        }
    }

    private void UpdatePreviewAtCurrentPosition()
    {
        if (!draggedItemVisual.IsDragging) return;
        ClearPreviewElements();

        VisualElement targetElement = GetElementAtPosition(lastMousePosition);
        string targetSlotType = GetSlotType(targetElement);
        if (targetElement != null && !string.IsNullOrEmpty(targetSlotType))
        {
            var (targetCategory, targetIdentifier, targetPosition) = ParseSlotType(targetSlotType);
            Item draggedItem = slotHandler.GetStackFromSlot(draggedItemVisual.SourceSlotType)?.Item;
            if (draggedItem != null)
            {
                // Special validation for bags
                if (draggedItem is Bag)
                {
                    var (sourceCategory, sourceIdentifier, _) = ParseSlotType(draggedItemVisual.SourceSlotType);

                    // Check if we're trying to drag an equipped bag to stash/sell
                    bool isEquippedBag = Enum.TryParse(draggedItemVisual.SourceSlotType, out EquipmentSlotType sourceSlotTypeEnum) &&
                                         sourceSlotTypeEnum == ((EquipmentBase)draggedItem).Slot;

                    // Don't allow dropping bag into its own container
                    if (isEquippedBag && targetCategory == "ContainerSlot" && targetIdentifier == ((EquipmentBase)draggedItem).Slot.ToString())
                    {
                        Debug.Log("Cannot drop a bag into its own container");
                        ShowInvalidOperationPreview(targetElement);
                        return;
                    }

                    // Check if we're trying to move a bag with items to stash/sell
                    if (isEquippedBag && (targetCategory == "StashSlot" || targetCategory == "SellSlot"))
                    {
                        // Get bag content to see if it has items
                        var bagSlot = equipmentManager.GetEquipmentSlot(((EquipmentBase)draggedItem).Slot);
                        if (bagSlot?.storageContainer != null)
                        {
                            var items = bagSlot.storageContainer.GetItems();
                            if (items != null && items.Any())
                            {
                                Debug.Log("Cannot move bag with items to stash/sell");
                                ShowInvalidOperationPreview(targetElement);
                                return;
                            }
                        }
                    }
                }

                InvItemContainer container = null;
                if (targetCategory == "ContainerSlot" && Enum.TryParse(targetIdentifier, out EquipmentSlotType eqType))
                {
                    var slot = equipmentManager.GetEquipmentSlot(eqType);
                    container = slot?.storageContainer;
                }
                else if (targetCategory == "StashSlot" || targetCategory == "SellSlot")
                {
                    var stashSystem = equipmentManager.GetComponent<StashSystem>();
                    container = targetCategory == "StashSlot"
                        ? stashSystem.GetStashContainer()
                        : stashSystem.GetSellContainer();
                }

                if (container != null)
                {
                    // Get the dimensions of the item being dragged
                    int w = draggedItemVisual.IsRotated ? draggedItem.SlotDimension.Height : draggedItem.SlotDimension.Width;
                    int h = draggedItemVisual.IsRotated ? draggedItem.SlotDimension.Width : draggedItem.SlotDimension.Height;

                    var targetStack = container.GetStackAtPosition(targetPosition);
                    bool canPlaceStorage = true;

                    // Special handling for bags
                    if (draggedItem is Bag)
                    {
                        // Check if we're moving a bag that's already in a container
                        var sourceSlotInfo = ParseSlotType(draggedItemVisual.SourceSlotType);
                        bool isBagInContainer = sourceSlotInfo.category == "ContainerSlot";

                        if (isBagInContainer)
                        {
                            // Bags in containers can be moved normally
                            canPlaceStorage = true;
                        }
                        else
                        {
                            // Otherwise use the normal check for equipped bags
                            canPlaceStorage = CanPlaceStorageEquipment(draggedItem);
                        }
                    }
                    else
                    {
                        canPlaceStorage = CanPlaceStorageEquipment(draggedItem);
                    }

                    if (!canPlaceStorage)
                    {
                        ShowPreviewCells(container, targetPosition, w, h, false, false, targetElement);
                        return;
                    }

                    // Check if target slots overlap with original slots
                    bool overlapsWithOriginal = CheckIfOverlapsWithOriginalPosition(targetPosition, w, h, targetIdentifier);

                    // Placement is valid if:
                    // 1. Target is empty and can place item OR
                    // 2. Overlaps with the item's original position (same container)
                    bool canPlace = (targetStack == null && container.CanPlaceItem(draggedItem, targetPosition, draggedItemVisual.IsRotated)) ||
                                  (targetIdentifier == sourceItemContainerId && overlapsWithOriginal);

                    bool isStackCombination = targetStack != null &&
                                              targetStack.Item == draggedItem &&
                                              !draggedItem.IsMultiSlot &&
                                              targetStack.Quantity < targetStack.Item.maxStack;

                    ShowPreviewCells(container, targetPosition, w, h, canPlace || isStackCombination, false, targetElement);
                }
            }
        }
        lastPreviewTarget = targetElement;
    }

    private void OnSlotMouseDown(MouseDownEvent evt)
    {
        if (!invUI.IsInventoryVisible()) return;
        shiftClickHandler.StopShiftDrag();

        VisualElement currentSlot = evt.currentTarget as VisualElement;
        if (currentSlot == null || !(currentSlot.userData is string slotType)) return;

        if (evt.ctrlKey && evt.button == 0)
        {
            var stack = slotHandler.GetStackFromSlot(slotType);
            if (stack != null && stack.Quantity > 1 && itemSplitter != null)
            {
                itemSplitter.ShowSplitPanel(slotType, stack);
                evt.StopPropagation();
                return;
            }
        }

        if (evt.shiftKey)
        {
            shiftClickHandler.HandleShiftClick(slotType);
            shiftClickHandler.StartShiftDrag(slotType);
            evt.StopPropagation();
            root.RegisterCallback<MouseMoveEvent>(OnShiftDragMouseMove);
        }

        if (IsDoubleClick(slotType))
        {
            HandleDoubleClick(slotType);
            return;
        }

        if (evt.button == 0)
        {
            mouseDownPosition = evt.mousePosition;
            if (evt.shiftKey)
            {
                shiftClickHandler.StartShiftDrag(slotType);
                evt.StopPropagation();
                return;
            }
            else
            {
                root.RegisterCallback<MouseMoveEvent>(OnMouseMoveStartDrag);
                root.RegisterCallback<MouseUpEvent>(OnMouseUpCancelDrag);

                if (draggedItemVisual.IsDragging)
                {
                    UpdateDropPreview(evt.mousePosition);
                }
            }
        }
        else if (evt.button == 1)
        {
            OnItemRightClick(slotType);
        }
    }

    private void OnItemRightClick(string slotType)
    {
        Item item = slotHandler.GetStackFromSlot(slotType)?.Item;
        if (item != null)
        {
            string displayName = !string.IsNullOrEmpty(item.PlayerGivenName) ? item.PlayerGivenName : item.itemName;
            Debug.Log($"Right-clicked on {displayName}");

            // Show context menu
            if (tooltipSystem != null)
            {
                Vector2 mousePos = Input.mousePosition;
                mousePos.y = Screen.height - mousePos.y; // Convert to UI coordinates
                tooltipSystem.ShowContextMenu(slotType, mousePos);
            }
        }
    }

    private void OnShiftDragMouseMove(MouseMoveEvent evt)
    {
        shiftClickHandler.HandleShiftDragging(evt.mousePosition);
        evt.StopPropagation();
    }

    private void OnGlobalMouseUp(MouseUpEvent evt)
    {
        if (!invUI.IsInventoryVisible()) return;
        shiftClickHandler.StopShiftDrag();

        if (draggedItemVisual.IsDragging)
        {
            UnregisterDragSlotEvents();
            FinalizeDragOperation(evt.mousePosition);
            root.UnregisterCallback<KeyDownEvent>(OnGlobalKeyDown);
            root.UnregisterCallback<MouseMoveEvent>(OnGlobalMouseMove);
            isDraggingItem = false;
            
            // Allow tooltips again after dragging
            tooltipSystem?.AllowTooltipsAfterDragging();

            // Make sure to clear any equipment slot highlights
            ClearEquipmentSlotHighlights();
        }
        lastPreviewTarget = null;
        UnhighlightCurrentSlot();
        ClearPreviewElements();
    }

    private void OnGlobalKeyDown(KeyDownEvent evt)
    {
        if (!invUI.IsInventoryVisible()) return;

        if (evt.keyCode == KeyCode.R && draggedItemVisual.IsDragging)
        {
            draggedItemVisual.RotateItem();
            UpdatePreviewAtCurrentPosition();
            evt.StopPropagation();
        }
    }

    private bool IsDoubleClick(string slotType)
    {
        bool isDoubleClick = (slotType == lastClickedSlot && Time.time - lastClickTime < doubleClickTime);
        lastClickTime = Time.time;
        lastClickedSlot = slotType;
        return isDoubleClick;
    }

    private void HandleDoubleClick(string slotType)
    {
        Item item = slotHandler.GetStackFromSlot(slotType)?.Item;
        if (item != null)
        {
            if (item is ConsumableDefinition consumable)
            {
                UseConsumable(consumable, slotType);
            }
        }
    }

    // Update the UseConsumable method to handle the new Manual type
    private void UseConsumable(ConsumableDefinition consumable, string slotType)
    {
        PlayerStatus playerStatus = equipmentManager.GetComponent<PlayerStatus>();
        if (playerStatus != null)
        {
            // Use the virtual Use method which allows Manual items to override behavior
            consumable.Use(playerStatus);
            
            // Remove the consumed item
            RemoveConsumedItem(slotType);
        }
    }

    private void RemoveConsumedItem(string slotType)
    {
        if (string.IsNullOrEmpty(slotType)) return;

        var (category, identifier, position) = ParseSlotType(slotType);

        if (category == "ContainerSlot" && Enum.TryParse(identifier, out EquipmentSlotType equipmentSlotType))
        {
            equipmentManager.RemoveItemFromContainer(equipmentSlotType,
                position.y * equipmentManager.GetEquipmentSlot(equipmentSlotType).storageContainer.GridWidth + position.x);
        }
        invUI.UpdateUI();
    }

    private void UnhighlightCurrentSlot()
    {
        if (currentHighlightedSlot != null)
        {
            currentHighlightedSlot.RemoveFromClassList("slot-dragging-over");
            currentHighlightedSlot.style.backgroundColor = StyleKeyword.Null;
            currentHighlightedSlot = null;
        }
    }

    private void OnMouseMoveStartDrag(MouseMoveEvent evt)
    {
        if (Vector2.Distance(mouseDownPosition, evt.mousePosition) > dragThreshold)
        {
            // Prevent tooltips while dragging
            tooltipSystem?.PreventTooltipsWhileDragging();
            
            root.UnregisterCallback<MouseMoveEvent>(OnMouseMoveStartDrag);
            root.UnregisterCallback<MouseUpEvent>(OnMouseUpCancelDrag);

            VisualElement currentSlot = GetElementAtPosition(mouseDownPosition);
            if (currentSlot != null && currentSlot.userData is string slotType)
            {
                var stack = slotHandler.GetStackFromSlot(slotType);
                if (stack == null) return;

                bool isRotated = false;
                var (category, identifier, gridCoord) = ParseSlotType(slotType);

                // Save source information for later comparison
                sourceItemPosition = gridCoord;
                sourceItemContainerId = identifier;
                isRotatedAtSource = false;
                sourceItemOccupiedSlots.Clear();

                InvItemContainer sourceContainer = null;

                if (category == "ContainerSlot" && Enum.TryParse(identifier, out EquipmentSlotType equipmentSlotType))
                {
                    var slot = equipmentManager.GetEquipmentSlot(equipmentSlotType);
                    if (slot?.storageContainer != null)
                    {
                        sourceContainer = slot.storageContainer;
                        sourceContainer.TryGetItemPosition(stack, out Vector2Int itemPos, out isRotated);
                        isRotatedAtSource = isRotated;
                    }
                }
                else if (category == "StashSlot" || category == "SellSlot")
                {
                    var stashSystem = equipmentManager.GetComponent<StashSystem>();
                    sourceContainer = category == "StashSlot"
                        ? stashSystem.GetStashContainer()
                        : stashSystem.GetSellContainer();
                    if (sourceContainer != null)
                    {
                        sourceContainer.TryGetItemPosition(stack, out Vector2Int itemPos, out isRotated);
                        isRotatedAtSource = isRotated;
                    }
                }

                // Record all slots the item occupies in its source position
                if (sourceContainer != null && stack.Item != null && stack.Item.IsMultiSlot)
                {
                    int width = isRotated ? stack.Item.SlotDimension.Height : stack.Item.SlotDimension.Width;
                    int height = isRotated ? stack.Item.SlotDimension.Width : stack.Item.SlotDimension.Height;

                    for (int y = 0; y < height; y++)
                    {
                        for (int x = 0; x < width; x++)
                        {
                            sourceItemOccupiedSlots.Add(new Vector2Int(sourceItemPosition.x + x, sourceItemPosition.y + y));
                        }
                    }
                }
                else
                {
                    // For single-slot items, just add the position
                    sourceItemOccupiedSlots.Add(sourceItemPosition);
                }

                // Create a deep copy of the ItemStack to track the item being dragged
                var originalItem = stack.Item;
                int originalQuantity = stack.Quantity;
                var clonedStack = new ItemStack(originalItem, originalQuantity);

                // Add the stack to tracked dragged items - both the original and the clone
                itemsBeingDragged.Add(stack);
                itemsBeingDragged.Add(clonedStack);

                draggedItemVisual.CreateDraggedItem(clonedStack, evt.mousePosition, slotType, isRotated);
                if (draggedItemVisual.IsDragging)
                {
                    originalSlot = currentSlot;
                    currentHighlightedSlot = null;
                    originalSlot.RemoveFromClassList("slot-hovered");
                    originalSlot.RemoveFromClassList("slot-dragging-over");

                    bool isEquipmentSlot = Enum.TryParse(slotType, out EquipmentSlotType _);
                    if (!isEquipmentSlot && originalSlot.parent != null)
                    {
                        originalSlot.parent.Remove(originalSlot);
                    }

                    lastMousePosition = evt.mousePosition;
                    RegisterDragSlotEvents();
                    UpdatePreview(evt.mousePosition);
                    isDraggingItem = true;
                }
            }
        }
    }

    private void RegisterDragSlotEvents()
    {
        var slots = root.Query<VisualElement>().Where(e =>
            e.userData is string slotType &&
            (slotType.StartsWith(CONTAINER_SLOT_PREFIX) ||
             Enum.TryParse(slotType as string, out EquipmentSlotType _))).ToList();

        foreach (var slot in slots)
        {
            slot.AddToClassList("drop-target");
            slot.pickingMode = PickingMode.Position;
        }
        root.RegisterCallback<KeyDownEvent>(OnGlobalKeyDown);
    }

    private void UnregisterDragSlotEvents()
    {
        var slots = root.Query<VisualElement>().Where(e =>
            e.userData is string slotType &&
            (slotType.StartsWith(CONTAINER_SLOT_PREFIX) ||
             Enum.TryParse(slotType as string, out EquipmentSlotType _))).ToList();

        foreach (var slot in slots)
        {
            slot.RemoveFromClassList("drop-target");
            slot.pickingMode = PickingMode.Position;
        }
        root.UnregisterCallback<KeyDownEvent>(OnGlobalKeyDown);
    }

    private void OnMouseUpCancelDrag(MouseUpEvent evt)
    {
        root.UnregisterCallback<MouseMoveEvent>(OnMouseMoveStartDrag);
        root.UnregisterCallback<MouseUpEvent>(OnMouseUpCancelDrag);
        ClearEquipmentSlotHighlights();
    }

    private VisualElement GetElementAtPosition(Vector2 position)
    {
        // No adjustment needed for initial mouse down or when not dragging
        // Only adjust position for preview/placement of multi-slot items
        Vector2 adjustedPosition = position;

        if (draggedItemVisual != null && draggedItemVisual.IsDragging && draggedItemVisual.DraggedItemStack?.Item != null)
        {
            // For multi-slot items, keep using the top-left slot as the anchor point
            // This maintains the original behavior while the visual is centered
            Item item = draggedItemVisual.DraggedItemStack.Item;
            if (item.IsMultiSlot)
            {
                // We need to offset the position by half the dimensions to get back to the top-left slot
                float cellSize = 64f; // Cell size in pixels
                int width = draggedItemVisual.IsRotated ? item.SlotDimension.Height : item.SlotDimension.Width;
                int height = draggedItemVisual.IsRotated ? item.SlotDimension.Width : item.SlotDimension.Height;

                // Calculate offset to find the top-left corner from the center
                float offsetX = (width * cellSize) / 2f;
                float offsetY = (height * cellSize) / 2f;

                // Apply offset to get the top-left slot
                adjustedPosition = new Vector2(position.x - offsetX + (cellSize / 2f), position.y - offsetY + (cellSize / 2f));
            }
        }

        VisualElement pickedElement = root.panel.Pick(adjustedPosition);

        while (pickedElement != null && pickedElement != root)
        {
            if (pickedElement.userData is string slotType)
            {
                if (slotType.StartsWith(CONTAINER_SLOT_PREFIX) ||
                    slotType.StartsWith(STASH_SLOT_PREFIX) ||
                    slotType.StartsWith(SELL_SLOT_PREFIX) ||
                    Enum.TryParse(slotType, out EquipmentSlotType _))
                {
                    return pickedElement;
                }
            }
            pickedElement = pickedElement.parent;
        }
        return null;
    }

    private string GetSlotType(VisualElement element)
    {
        return element?.userData as string;
    }

    private void FinalizeDragOperation(Vector2 mousePosition)
    {
        if (!invUI.IsInventoryVisible()) return;
        if (draggedItemVisual.IsDragging)
        {
            string sourceSlotType = draggedItemVisual.SourceSlotType;
            ItemStack sourceStack = draggedItemVisual.DraggedItemStack;
            if (sourceStack == null || sourceStack.Item == null)
            {
                Debug.Log("Finalizing drag but source stack is null");
                ClearPreviewElements();
                draggedItemVisual.CleanupDraggedItem();
                isDraggingItem = false;
                
                // Allow tooltips again after dragging
                tooltipSystem?.AllowTooltipsAfterDragging();
                return;
            }

            // Store the original stack's item before clearing
            Item originalItem = sourceStack.Item;
            int originalQuantity = sourceStack.Quantity;

            // Note: DON'T clear itemsBeingDragged yet - we need it during item moving operations
            // to prevent the wrong items from being considered as "dragged"
            // Will clear it after the move operation

            ClearEquipmentSlotHighlights();
            VisualElement targetElement = GetElementAtPosition(mousePosition);
            string targetSlotType = GetSlotType(targetElement);
            ClearPreviewElements();

            // Hide the dragged item visual but don't clean up yet
            draggedItemVisual.Hide();

            // Check if we're outside any UI element (for dropping in the world)
            bool isOutsideUI = !IsPointerInsideInventoryUI(mousePosition);
            Debug.Log($"Finalizing drag operation. Item: {originalItem.itemName}, Outside UI: {isOutsideUI}, Target slot: {targetSlotType}");

            bool shouldAttemptMove = !string.IsNullOrEmpty(targetSlotType) || isOutsideUI;
            bool handled = false;

            // Check if the operation involves the sell container
            bool involvesSellContainer = false;
            if (!string.IsNullOrEmpty(targetSlotType))
            {
                var (targetCategory, _, _) = ParseSlotType(targetSlotType);
                involvesSellContainer = (targetCategory == "SellSlot");
            }

            // Special validation for bags
            if (originalItem is Bag && !string.IsNullOrEmpty(targetSlotType))
            {
                var (targetCategory, targetIdentifier, _) = ParseSlotType(targetSlotType);
                var (sourceCategory, sourceIdentifier, _) = ParseSlotType(sourceSlotType);

                // Check if we're trying to drag an equipped bag
                bool isEquippedBag = Enum.TryParse(sourceSlotType, out EquipmentSlotType sourceSlotTypeEnum) &&
                                    sourceSlotTypeEnum == ((EquipmentBase)originalItem).Slot;

                // Don't allow dropping bag into its own container
                if (isEquippedBag && targetCategory == "ContainerSlot" &&
                    targetIdentifier == ((EquipmentBase)originalItem).Slot.ToString())
                {
                    Debug.Log("Cannot drop a bag into its own container");
                    handled = false;
                    shouldAttemptMove = false;
                }

                // Check if we're trying to move a bag with items to stash/sell
                if (isEquippedBag && (targetCategory == "StashSlot" || targetCategory == "SellSlot"))
                {
                    // Get bag content to see if it has items
                    var bagSlot = equipmentManager.GetEquipmentSlot(((EquipmentBase)originalItem).Slot);
                    if (bagSlot?.storageContainer != null)
                    {
                        var items = bagSlot.storageContainer.GetItems();
                        if (items != null && items.Any())
                        {
                            Debug.Log("Cannot move bag with items to stash/sell");
                            handled = false;
                            shouldAttemptMove = false;
                        }
                    }
                }
            }

            // Check if source and target containers are different
            bool isCrossContainerMove = false;
            if (!string.IsNullOrEmpty(targetSlotType))
            {
                var (sourceCategory, sourceIdentifier, _) = ParseSlotType(sourceSlotType);
                var (targetCategory, targetIdentifier, _) = ParseSlotType(targetSlotType);

                isCrossContainerMove = (sourceCategory != targetCategory || sourceIdentifier != targetIdentifier);
            }

            if (shouldAttemptMove)
            {
                try
                {
                    if (!string.IsNullOrEmpty(targetSlotType))
                    {
                        var (targetCategory, _, _) = ParseSlotType(targetSlotType);

                        // IMPORTANT: Use a deep copy of the stack to avoid reference issues
                        ItemStack stackToMove = new ItemStack(originalItem, originalQuantity);
                        handled = itemMover.MoveItem(sourceSlotType, targetSlotType, draggedItemVisual.IsRotated, stackToMove);
                        Debug.Log($"Attempted to move item to {targetSlotType}, success: {handled}");
                    }
                    if (!handled && isOutsideUI)
                    {
                        Debug.Log("Dropping item outside inventory: " + originalItem.itemName);
                        HandleItemDrop(sourceSlotType, mousePosition);
                        slotHandler.RemoveStackFromSlot(sourceSlotType);
                        handled = true;
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error during drag operation: {ex.Message}");
                    handled = false;
                }
            }

            if (!handled)
            {
                Debug.Log($"Item move not handled, returning item to source slot: {sourceSlotType}");
                slotHandler.SetStackInSlot(sourceSlotType, new ItemStack(originalItem, originalQuantity));
            }

            // Clear dragging state AFTER item operations are complete
            isDraggingItem = false;
            itemsBeingDragged.Clear();
            
            // Allow tooltips again after dragging
            tooltipSystem?.AllowTooltipsAfterDragging();

            // Always force the UI update
            invUI.UpdateUI();

            if (uiPanelManager != null && uiPanelManager.IsStashOpen)
            {
                stashSystem.SaveStashData();
            }

            // If the operation involved the sell container, update the ShopUI as well
            if (involvesSellContainer)
            {
                var shopUI = FindObjectOfType<ShopUI>();
                if (shopUI != null)
                {
                    shopUI.UpdateUI();
                }
            }

            // Final cleanup - do this AFTER UI update to ensure visual consistency
            ResetOriginalSlotOpacity();
            lastPreviewTarget = null;
            draggedItemVisual.CleanupDraggedItem();
            originalSlot = null;
            UnhighlightCurrentSlot();
        }
    }

    private void HandleItemDrop(string sourceSlotType, Vector2 mousePosition)
    {
        Debug.Log($"HandleItemDrop called for slot {sourceSlotType} at position {mousePosition}");

        // Actually spawn item in the world via server call:
        ItemStack stackToDrop = slotHandler.GetStackFromSlot(sourceSlotType);
        if (stackToDrop?.Item != null)
        {
            bool isEquipment = stackToDrop.Item is EquipmentBase;
            int equipmentSlotTypeInt = -1;
            string serializedContainerContent = "";
            bool isBagInContainer = false;

            // Parse the source slot type to determine if this is a bag in a container
            var (sourceCategory, sourceIdentifier, _) = ParseSlotType(sourceSlotType);
            isBagInContainer = isEquipment && stackToDrop.Item is Bag && sourceCategory == "ContainerSlot";

            if (isEquipment)
            {
                EquipmentBase equipmentItem = (stackToDrop.Item as EquipmentBase);
                EquipmentSlotType equipmentSlotType = equipmentItem.Slot;
                equipmentSlotTypeInt = (int)equipmentSlotType;

                // If item is Bag, serialize its container content ONLY if it's directly equipped
                // We should NOT call UnequipItem for bags in containers
                if (stackToDrop.Item is Bag)
                {
                    // Only serialize and unequip if it's the equipped bag
                    if (!isBagInContainer && Enum.TryParse(sourceSlotType, out EquipmentSlotType directEquipSlot)
                        && directEquipSlot == equipmentSlotType)
                    {
                        serializedContainerContent = equipmentManager.SerializeContainerContent(equipmentSlotType);
                        equipmentManager.UnequipItem(equipmentSlotType, true);
                        Debug.Log($"Unequipped bag from equipment slot {equipmentSlotType}");
                    }
                    else
                    {
                        // For bags in containers, we don't need to unequip anything
                        Debug.Log($"Dropping bag from container - not unequipping equipment");
                    }
                }
                else
                {
                    // For non-bag equipment that's directly equipped, unequip it
                    if (!isBagInContainer && Enum.TryParse(sourceSlotType, out EquipmentSlotType directEquipSlot)
                        && directEquipSlot == equipmentSlotType)
                    {
                        equipmentManager.UnequipItem(equipmentSlotType, true);
                    }
                }
            }

            if (!string.IsNullOrEmpty(stackToDrop.Item.itemName))
            {
                Debug.Log($"Creating dropped item in world: {stackToDrop.Item.itemName} x{stackToDrop.Quantity}");
                CreateDroppedItem(
                    stackToDrop.Item.itemName,
                    mousePosition,
                    isEquipment,
                    equipmentSlotTypeInt,
                    serializedContainerContent,
                    stackToDrop.Quantity
                );
            }
            else
            {
                Debug.LogError($"Cannot drop item with null or empty name. Item: {stackToDrop.Item}");
            }

            invUI.UpdateUI();
        }
        else
        {
            Debug.LogError("Attempted to drop null item stack");
        }
    }

    private void CreateDroppedItem(string itemName, Vector2 position, bool isEquipment, int equipmentSlotTypeInt, string serializedContainerContent, int quantity)
    {
        itemDropping.CreateDroppedItemInstance(gameObject, itemName, position, isEquipment, equipmentSlotTypeInt, serializedContainerContent, quantity);
    }

    private void ResetOriginalSlotOpacity()
    {
        if (originalSlot != null)
        {
            Image slotImage = originalSlot.Q<Image>();
            if (slotImage != null)
            {
                slotImage.style.opacity = 1f;
            }
        }
    }

    private (string category, string identifier, Vector2Int position) ParseSlotType(string slotType)
    {
        var info = InvItemUtils.ParseSlotType(slotType, equipmentManager);
        return (info.Category, info.Identifier, info.Position);
    }

    private void UpdateDropPreview(Vector2 mousePosition)
    {
        if (!draggedItemVisual.IsDragging) return;

        // Check if we're outside any inventory UI
        bool isOutsideUI = !IsPointerInsideInventoryUI(mousePosition);

        // Update the dropping visualization
        if (isOutsideUI != draggedItemVisual.IsDraggingOutside)
        {
            draggedItemVisual.IsDraggingOutside = isOutsideUI;
            draggedItemVisual.UpdateDroppingLabel();
        }

        // Only update the last mouse position
        lastMousePosition = mousePosition;
        UpdatePreviewAtCurrentPosition();
    }

    private bool CanPlaceStorageEquipment(Item item)
    {
        if (item is EquipmentBase equipment && equipment is Bag)
        {
            // Get source slot info to determine context
            string sourceSlotType = draggedItemVisual?.SourceSlotType;
            if (string.IsNullOrEmpty(sourceSlotType))
                return true;

            var (sourceCategory, sourceIdentifier, _) = ParseSlotType(sourceSlotType);
            bool isBagInContainer = sourceCategory == "ContainerSlot";
            bool isEquippedBag = Enum.TryParse(sourceSlotType, out EquipmentSlotType sourceSlotTypeEnum) &&
                                sourceSlotTypeEnum == equipment.Slot;

            // If it's a bag in a container, allow it for most operations
            if (isBagInContainer)
            {
                Debug.Log("Allowing bag placement since it's coming from a container");
                return true;
            }

            // For equipped bags, we need to check if they have items
            var currentSlot = equipmentManager.GetEquipmentSlot(equipment.Slot);
            if (currentSlot?.storageContainer != null)
            {
                var items = currentSlot.storageContainer.GetItems();
                bool hasItems = items != null && items.Any();

                // If the bag has items, it can only be placed in specific locations
                if (hasItems)
                {
                    Debug.Log("Bag has items - restricting where it can be placed");
                    return false;
                }
            }
        }
        return true;
    }

    private void ShowPreviewCells(InvItemContainer container, Vector2Int position, int width, int height,
        bool canDrop, bool wouldSwap, VisualElement targetSlot)
    {
        bool isStackCombination = false;
        if (draggedItemVisual.DraggedItemStack != null)
        {
            var targetStack = container.GetStackAtPosition(position);
            if (targetStack != null &&
                targetStack.Item == draggedItemVisual.DraggedItemStack.Item &&
                !targetStack.Item.IsMultiSlot &&
                targetStack.Quantity < targetStack.Item.maxStack)
            {
                isStackCombination = true;
                canDrop = true;
            }
        }

        // Don't center the preview - this keeps visual alignment with placement logic
        // We need position to remain as the top-left anchor point for the item

        VisualElement parentContainer = targetSlot.parent ?? targetSlot;
        float cellSize = 64f;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                Vector2Int cellPos = new Vector2Int(position.x + x, position.y + y);
                if (cellPos.x < container.GridWidth && cellPos.y < container.GridHeight)
                {
                    CreatePreviewCell(parentContainer, cellPos, canDrop, false, cellSize, isStackCombination);
                }
            }
        }
    }

    private void CreatePreviewCell(VisualElement parent, Vector2Int gridPosition, bool canDrop, bool wouldSwap,
        float cellSize, bool isStackCombination = false)
    {
        var previewCell = new VisualElement
        {
            style =
            {
                position = Position.Absolute,
                left = gridPosition.x * cellSize,
                top = gridPosition.y * cellSize,
                width = cellSize,
                height = cellSize,
                backgroundColor = new StyleColor(GetPreviewColor(canDrop, wouldSwap, isStackCombination))
            }
        };
        previewCell.pickingMode = PickingMode.Ignore;
        parent.Add(previewCell);
        previewElements.Add(previewCell);
    }

    private Color GetPreviewColor(bool canDrop, bool wouldSwap = false, bool isStackCombination = false)
    {
        if (isStackCombination)
        {
            return new Color(0, 1, 0, 0.4f);
        }
        return canDrop
            ? new Color(0, 1, 0, 0.2f)
            : new Color(1, 0, 0, 0.3f);
    }

    private void ClearPreviewElements()
    {
        foreach (var element in previewElements)
        {
            element.RemoveFromHierarchy();
        }
        previewElements.Clear();
    }

    private void UpdateEquipmentSlotHighlights(Item draggedItem)
    {
        // First clear any existing highlights
        ClearEquipmentSlotHighlights();

        if (draggedItem is EquipmentBase equipment)
        {
            var targetSlotId = equipment.Slot.ToString() + "Icon";
            var targetSlotImage = root.Q<Image>(targetSlotId);
            var targetSlot = targetSlotImage?.parent;
            if (targetSlot != null)
            {
                var currentlyEquipped = equipmentManager.GetEquippedItem(equipment.Slot);
                if (currentlyEquipped == null)
                {
                    // Find the equipment-slot element that contains this slot
                    VisualElement equipmentSlotElement = targetSlot;
                    while (equipmentSlotElement != null)
                    {
                        if (equipmentSlotElement.ClassListContains("equipment-slot"))
                        {
                            // We found the equipment-slot element, add the valid-target class to it
                            equipmentSlotElement.AddToClassList("valid-target");
                            break;
                        }
                        equipmentSlotElement = equipmentSlotElement.parent;
                    }

                    // If we couldn't find an equipment-slot parent, add the valid-target class to the slot itself
                    if (equipmentSlotElement == null)
                    {
                        targetSlot.AddToClassList("valid-target");
                    }
                }
            }
        }
    }

    private void ClearEquipmentSlotHighlights()
    {
        // Clear the valid-target class from all slots with userData
        var slots = root.Query<VisualElement>().Where(e =>
            e.userData is string slotType &&
            Enum.TryParse(slotType as string, out EquipmentSlotType _)).ToList();

        foreach (var slot in slots)
        {
            slot.RemoveFromClassList("valid-target");
        }

        // Also clear from all equipment-slot elements
        var equipmentSlots = root.Query<VisualElement>(className: "equipment-slot").ToList();
        foreach (var slot in equipmentSlots)
        {
            slot.RemoveFromClassList("valid-target");

            // Ensure proper empty/filled class is maintained
            EquipmentSlotType slotType;
            if (slot.name == "HeadSlot")
                slotType = EquipmentSlotType.HeadSlot;
            else if (slot.name == "ChestSlot")
                slotType = EquipmentSlotType.ChestSlot;
            else if (slot.name == "BagSlot")
                slotType = EquipmentSlotType.BagSlot;
            else
                continue;

            var equippedItem = equipmentManager.GetEquippedItem(slotType);
            if (equippedItem != null)
            {
                slot.AddToClassList("filled");
                slot.RemoveFromClassList("empty");
            }
            else
            {
                slot.RemoveFromClassList("filled");
                slot.AddToClassList("empty");
            }
        }
    }

    private bool CheckIfOverlapsWithOriginalPosition(Vector2Int targetPosition, int width, int height, string containerId)
    {
        // Only consider overlaps if we're in the same container
        if (containerId != sourceItemContainerId) return false;

        // Check if any target slot overlaps with original slots
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                Vector2Int checkPos = new Vector2Int(targetPosition.x + x, targetPosition.y + y);

                // If target is blocked by a slot that our source item occupies, that's okay
                if (sourceItemOccupiedSlots.Contains(checkPos))
                {
                    return true;
                }
            }
        }

        return false;
    }

    // Add this method to the class to ensure hover effects are properly restored
    public void RestoreHoverStates()
    {
        // Restore hover states from our persistent registry
        foreach (string slotType in persistentHoverSlots)
        {
            var slot = FindSlotByType(slotType);
            if (slot != null)
            {
                var stack = slotHandler.GetStackFromSlot(slotType);
                if (stack != null && stack.Item != null)
                {
                    // Check if it already has the hover class
                    if (!slot.ClassListContains("slot-hovered"))
                    {
                        slot.AddToClassList("slot-hovered");
                    }
                }
            }
        }
    }

    // Method to clear all hover states when inventory closes
    public void ClearAllHoverStates()
    {
        // Clear all persistent hover states
        foreach (string slotType in persistentHoverSlots.ToList())
        {
            var slot = FindSlotByType(slotType);
            if (slot != null)
            {
                UpdateSlotVisualFeedback(slot, false);
            }
        }

        persistentHoverSlots.Clear();
        hoveredSlots.Clear();
        hoveredSlot = null;

        // Hide any active tooltips
        if (tooltipSystem != null)
        {
            tooltipSystem.HideTooltip();
        }
    }

    // Helper method to find a slot by its type
    private VisualElement FindSlotByType(string slotType)
    {
        var slots = root.Query<VisualElement>().Where(e => e.userData != null && e.userData.ToString() == slotType).ToList();
        return slots.Count > 0 ? slots[0] : null;
    }

    // Helper method to show an invalid operation preview
    private void ShowInvalidOperationPreview(VisualElement targetElement)
    {
        var parentElement = targetElement.parent ?? targetElement;
        var previewCell = new VisualElement
        {
            style =
            {
                position = Position.Absolute,
                left = 0,
                top = 0,
                width = parentElement.contentRect.width,
                height = parentElement.contentRect.height,
                backgroundColor = new Color(1, 0, 0, 0.3f)
            }
        };
        previewCell.pickingMode = PickingMode.Ignore;
        parentElement.Add(previewCell);
        previewElements.Add(previewCell);
    }

    private void ShowBatteryPreviewForItem(Item item)
    {
        Debug.Log($"[InvDragAndDropManager] ShowBatteryPreviewForItem called with item: {item?.name}");

        // Check if the item is a consumable with energy restore
        if (item is ConsumableDefinition consumable && consumable.EnergyRestore > 0)
        {
            Debug.Log($"[InvDragAndDropManager] Item is consumable with {consumable.EnergyRestore} energy restore");

            // Find the battery controller that's attached to the same GameObject as InvUI
            var batteryController = invUI.GetComponent<BatteryController>();
            if (batteryController == null)
            {
                // Try to find it in the scene
                batteryController = FindFirstObjectByType<BatteryController>();
            }

            if (batteryController != null)
            {
                Debug.Log($"[InvDragAndDropManager] Found battery controller, showing preview");
                batteryController.ShowConsumablePreview(consumable.EnergyRestore);
            }
            else
            {
                Debug.LogWarning($"[InvDragAndDropManager] Battery controller not found!");
            }
        }
        else
        {
            Debug.Log($"[InvDragAndDropManager] Item is not a consumable or has no energy restore. Type: {item?.GetType().Name}");
        }
    }

    private void HideBatteryPreview()
    {
        // Find the battery controller that's attached to the same GameObject as InvUI
        var batteryController = invUI.GetComponent<BatteryController>();
        if (batteryController == null)
        {
            // Try to find it in the scene
            batteryController = FindFirstObjectByType<BatteryController>();
        }

        if (batteryController != null)
        {
            batteryController.HideConsumablePreview();
        }
    }
}
