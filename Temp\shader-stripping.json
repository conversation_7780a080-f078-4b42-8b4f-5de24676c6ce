{"totalVariantsIn": 370643, "totalVariantsOut": 4144, "shaders": [{"inputVariants": 6, "outputVariants": 6, "name": "Shader Graphs/PhysicallyBasedSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7996000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.10490000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0541}]}]}, {"inputVariants": 434, "outputVariants": 122, "name": "Shader Graphs/Water", "pipelines": [{"inputVariants": 434, "outputVariants": 122, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0707}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5465}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4969}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5571}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Hull)", "stripTimeMs": 0.2817}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Domain)", "stripTimeMs": 0.2851}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0351}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4575}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5947}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3383}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3362}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7022}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0604}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10200000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0541}]}]}, {"inputVariants": 37, "outputVariants": 20, "name": "Shader Graphs/SolidColor", "pipelines": [{"inputVariants": 37, "outputVariants": 20, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5679000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054}, {"inputVariants": 2, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0539}, {"inputVariants": 4, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0999}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0534}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0937}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0698}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.064}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0112}, {"inputVariants": 2, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0787}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1255}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.07300000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.07590000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.07060000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0734}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "HDRP/DefaultFogVolume", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5361}, {"inputVariants": 2, "outputVariants": 2, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0557}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0329}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.053000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0511}, {"inputVariants": 2, "outputVariants": 2, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08710000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Sample Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.1535}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0558}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054700000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0529}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Skybox/Cubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0229}]}]}, {"inputVariants": 144, "outputVariants": 144, "name": "Hidden/HDRP/Sky/CloudLayer", "pipelines": [{"inputVariants": 144, "outputVariants": 144, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5168}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.4067}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4333}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.43710000000000004}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Waveform", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0179}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/DLSSBiasColorMask", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.45070000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0375}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/WaterCaustics", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4293}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031900000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ApplyDistortion", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41240000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0269}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CameraMotionVectors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3163}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0194}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CompositeUI", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.40840000000000004}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.083}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/VoxelizeShader", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3241}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018600000000000002}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "Hidden/HDRP/DebugExposure", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.006}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/GGXConvolve", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.30760000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_GGXDisneyDiffuse", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3105}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/CoreSRP/CoreCopy", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4833}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0507}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019700000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/UpsampleTransparent", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3161}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0193}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0196}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019700000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031100000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/Sky/GradientSky", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3221}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0201}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019100000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CombineLighting", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0245}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0204}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ColorPyramidPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3709}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0284}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_<PERSON>ner", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43860000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0275}]}]}, {"inputVariants": 44, "outputVariants": 24, "name": "Hidden/HDRP/OpaqueAtmosphericScattering", "pipelines": [{"inputVariants": 44, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3145}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0449}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0201}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0405}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0211}, {"inputVariants": 16, "outputVariants": 8, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1095}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0201}, {"inputVariants": 16, "outputVariants": 8, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1101}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearBlack", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43510000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0359}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VFX/Empty", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3408}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.38280000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025500000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialLoading", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.44530000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031100000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/ProbeVolumeFragmentationDebug", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3149}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0204}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/Core/ProbeVolumeDebug", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_CharlieFabricLambert", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3776}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020200000000000003}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CopyStencilBuffer", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.456}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0378}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0182}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0182}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0182}]}]}, {"inputVariants": 1284, "outputVariants": 1284, "name": "Hidden/HDRP/TemporalAA", "pipelines": [{"inputVariants": 1284, "outputVariants": 1284, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3982}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.1053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.056400000000000006}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 5.2766}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0533}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 5.1786}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0434}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 2.8532}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CopyDepthBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3642}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0229}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewMaterialGBuffer", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0071}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/XROcclusionMesh", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3395}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/IntegrateHDRI", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.305}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0196}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/ChromaKeying", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2994}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018500000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Renderers/Thickness", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3281}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0441}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.043000000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/AlphaInjection", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3171}, {"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023700000000000002}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/DebugVTBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0068000000000000005}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/CharlieConvolve", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.008}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeToPano", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.389}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0223}]}]}, {"inputVariants": 769, "outputVariants": 257, "name": "Hidden/HDRP/FinalPass", "pipelines": [{"inputVariants": 769, "outputVariants": 257, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3145}, {"inputVariants": 768, "outputVariants": 256, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 7.928100000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/VrsVisualization", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.36610000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0206}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/DownsampleDepth", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031100000000000003}]}]}, {"inputVariants": 288790, "outputVariants": 810, "name": "HDRP/Lit", "pipelines": [{"inputVariants": 288790, "outputVariants": 810, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 120, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.3447}, {"inputVariants": 5184, "outputVariants": 240, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 31.397100000000002}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1728}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1343}, {"inputVariants": 144, "outputVariants": 48, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.3644}, {"inputVariants": 360, "outputVariants": 90, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.1411000000000002}, {"inputVariants": 72, "outputVariants": 24, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6647000000000001}, {"inputVariants": 144, "outputVariants": 36, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.1295}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3433}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3332}, {"inputVariants": 144, "outputVariants": 12, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6535000000000001}, {"inputVariants": 93312, "outputVariants": 36, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 407.68080000000003}, {"inputVariants": 144, "outputVariants": 72, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.4868000000000001}, {"inputVariants": 186624, "outputVariants": 156, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 838.5658000000001}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.19820000000000002}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.14}, {"inputVariants": 4, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0179}, {"inputVariants": 12, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0391}, {"inputVariants": 864, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 5.9779}, {"inputVariants": 432, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 4.3941}, {"inputVariants": 864, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 7.1113}, {"inputVariants": 12, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.36000000000000004}, {"inputVariants": 72, "outputVariants": 0, "variantName": "SubSurfaceDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.7694000000000001}, {"inputVariants": 6, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1844}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.9775}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/DepthValues", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3639}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0488}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0253}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0386}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0198}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0397}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0188}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038200000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.468}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Vectorscope", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0094}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CustomPassUtils", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4328}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0269}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0264}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0199}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0179}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugColorPicker", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0064}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLocalVolumetricFogAtlas", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0056}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0055000000000000005}]}]}, {"inputVariants": 26, "outputVariants": 26, "name": "Hidden/HDRP/LensFlareDataDriven", "pipelines": [{"inputVariants": 26, "outputVariants": 26, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3401}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0488}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.030000000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0478}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0303}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0458}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035300000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0509}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0196}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018600000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/FallbackError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.35650000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0307}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/CompositeLines", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.30720000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0234}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0193}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0182}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0229}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLightVolumes", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearStencilBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3138}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019200000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ScriptableRenderPipeline/DebugDisplayHDShadowMap", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3536}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0274}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/PostProcessing/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.33580000000000004}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.042100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0251}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0403}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041800000000000004}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/LensFlareScreenSpace", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.36160000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0224}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020200000000000003}]}]}, {"inputVariants": 3, "outputVariants": 0, "name": "Hidden/HDRP/DebugHDR", "pipelines": [{"inputVariants": 3, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.006}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 868, "outputVariants": 52, "name": "Hidden/HDRP/Sky/HDRISky", "pipelines": [{"inputVariants": 868, "outputVariants": 52, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3005}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.9247000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0211}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.3088}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0221}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.9774}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0201}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.1347}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CustomClear", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.38580000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0206}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026500000000000003}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/Sky/PbrSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41700000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0298}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0303}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_CookTorrance", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4174}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027200000000000002}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/BlitColorAndDepth", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3672}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0804}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0506}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.047}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOccluder", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41250000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023700000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/WaterExclusion", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5386000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0308}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeSamplingDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.32530000000000003}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043500000000000004}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/ScreenSpaceShadows", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.01}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugDisplayLatlong", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/CoreResources/FilterAreaLightCookies", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.316}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0198}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0198}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018600000000000002}]}]}, {"inputVariants": 86, "outputVariants": 48, "name": "HDRP/Unlit", "pipelines": [{"inputVariants": 86, "outputVariants": 48, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34740000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.13340000000000002}, {"inputVariants": 3, "outputVariants": 3, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.043300000000000005}, {"inputVariants": 12, "outputVariants": 12, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1322}, {"inputVariants": 6, "outputVariants": 3, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054200000000000005}, {"inputVariants": 12, "outputVariants": 6, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0844}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0383}, {"inputVariants": 6, "outputVariants": 6, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06420000000000001}, {"inputVariants": 3, "outputVariants": 0, "variantName": "DistortionVectors (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.026500000000000003}, {"inputVariants": 6, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029900000000000003}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1097}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1071}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.095}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1291}, {"inputVariants": 2, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0787}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0857}]}]}, {"inputVariants": 16, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewTiles", "pipelines": [{"inputVariants": 16, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 16, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0405}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3537}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0207}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/CustomPassRenderersUtils", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3378}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032600000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031100000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0327}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0304}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0309}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0516}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugBlitQuad", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0078000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0058000000000000005}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/HDRP/WaterDecal", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.33}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0184}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0199}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.02}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0198}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3214}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0181}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_Ward", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.305}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0263}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOcclusionTest", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3284}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0213}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/SRP/BlitCubeTextureFace", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3628}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023200000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader Graph/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3392}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0303}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeOffsetDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4198}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0723}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/ColorResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.31270000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0239}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018000000000000002}]}]}, {"inputVariants": 200, "outputVariants": 200, "name": "Hidden/HDRP/Blit", "pipelines": [{"inputVariants": 200, "outputVariants": 200, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3653}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.045700000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0839}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0473}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0463}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.048100000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0448}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.045700000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0816}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0468}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0782}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0449}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.079}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07740000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055400000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061900000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046700000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0745}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0694}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0455}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0446}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.045200000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046900000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0458}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0458}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.047900000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0465}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.047400000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0463}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07680000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0461}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07730000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0477}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.085}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0466}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0458}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0459}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0455}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0791}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0461}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0472}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0465}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08080000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0829}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0864}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08310000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowClear", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4439}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031100000000000003}]}]}, {"inputVariants": 5, "outputVariants": 3, "name": "Hidden/HDRP/XRMirrorView", "pipelines": [{"inputVariants": 5, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41490000000000005}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054700000000000006}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugFullScreen", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.006500000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/MotionVecResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3128}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0224}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0201}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0195}]}]}, {"inputVariants": 3, "outputVariants": 2, "name": "Hidden/HDRP/Material/Decal/DecalNormalBuffer", "pipelines": [{"inputVariants": 3, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3271}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030600000000000002}]}]}, {"inputVariants": 73, "outputVariants": 34, "name": "Shader Graphs/TIPS_Mesh 2", "pipelines": [{"inputVariants": 73, "outputVariants": 34, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4949}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1689}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0695}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1233}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1678}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.19820000000000002}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1694}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1012}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0166}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1306}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.221}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.12380000000000001}, {"inputVariants": 4, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.12490000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0654}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1253}]}]}, {"inputVariants": 59, "outputVariants": 24, "name": "Shader Graphs/TIPS_Mesh", "pipelines": [{"inputVariants": 59, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3941}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0843}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08510000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.17880000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0932}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.11950000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10490000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.10300000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.010100000000000001}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1263}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.26580000000000004}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1246}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0697}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1757}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2281}]}]}, {"inputVariants": 22211, "outputVariants": 90, "name": "Samples/SamplesLit_Inter", "pipelines": [{"inputVariants": 22211, "outputVariants": 90, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0191000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.10070000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.158}, {"inputVariants": 32, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3428}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0714}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0697}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0164}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3024}, {"inputVariants": 48, "outputVariants": 12, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6118}, {"inputVariants": 16, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3764}, {"inputVariants": 576, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.1187000000000005}, {"inputVariants": 16, "outputVariants": 8, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.28650000000000003}, {"inputVariants": 20736, "outputVariants": 20, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 88.61370000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0218}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 4.0585}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.4983}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.8029000000000001}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.2713}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1265}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.1338000000000001}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Unlit/Texture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053700000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0247}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "FullScreen/test", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3376}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0195}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Distance Field", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3539}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0765}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/EdgePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3144}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0193}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "FullScreen/TIPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.30910000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0195}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0182}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/VertexPicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3407}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0187}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/FacePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018600000000000002}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Sprite", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34640000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046200000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/HideVertices", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4103}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "TextMeshPro/Mobile/Distance Field", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5476}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.12380000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Legacy Shaders/Particles/Alpha Blended", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033100000000000004}]}]}, {"inputVariants": 21844, "outputVariants": 64, "name": "ProBuilder6/Standard Vertex Color", "pipelines": [{"inputVariants": 21844, "outputVariants": 64, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5761000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0594}, {"inputVariants": 4, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1433}, {"inputVariants": 16, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2747}, {"inputVariants": 2, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.013000000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.010100000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.19440000000000002}, {"inputVariants": 24, "outputVariants": 6, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3116}, {"inputVariants": 8, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1135}, {"inputVariants": 288, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 2.0027}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1967}, {"inputVariants": 20736, "outputVariants": 16, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 97.67450000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.017400000000000002}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.3334}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1388}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.6301}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.2216}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0814}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.094}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}, {"inputVariants": 0, "outputVariants": 0, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 32887, "outputVariants": 138, "name": "Shader Graphs/BakeryVolumeGraph", "pipelines": [{"inputVariants": 32887, "outputVariants": 138, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.5923000000000003}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0813}, {"inputVariants": 6, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3663}, {"inputVariants": 48, "outputVariants": 18, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.5019}, {"inputVariants": 3, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0426}, {"inputVariants": 3, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0223}, {"inputVariants": 6, "outputVariants": 6, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.38620000000000004}, {"inputVariants": 72, "outputVariants": 18, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.9455}, {"inputVariants": 12, "outputVariants": 6, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.33180000000000004}, {"inputVariants": 864, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 5.9514000000000005}, {"inputVariants": 12, "outputVariants": 6, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5153}, {"inputVariants": 31104, "outputVariants": 24, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 152.3728}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0361}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.3059000000000003}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.31}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.5489}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 4.4729}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0937}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.5588000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "HDRPSamples/LocalClouds", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.9934000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0487}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0574}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0716}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0609}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033800000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "CustomPass_SG/Outline", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.4897}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057800000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0431}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 59, "outputVariants": 24, "name": "Shader Graphs/Glitch_SG", "pipelines": [{"inputVariants": 59, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5614}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.14020000000000002}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1022}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.20500000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10640000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1027}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2053}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1405}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0188}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.248}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2363}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2081}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1046}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0653}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1597}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/BillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0344}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0264}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Bark Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0361}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0253}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Leaves Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0258}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0251}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CameraFacingBillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0359}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Albedo Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0304}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Normal Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0307}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/Splatmap/Standard-BaseGen", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0315}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0291}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0223}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0292}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021400000000000002}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/TerrainEngine/PaintHeight", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0222}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.036000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0212}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.020900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0204}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/HeightBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0307}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/GenerateNormalmap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0268}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0216}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/TerrainLayerUtils", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.036000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0347}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0286}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0296}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0341}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CrossBlendNeighbors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0369}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0412}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Mobile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.497}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Standard", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4171}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.053700000000000005}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0889}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0417}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0297}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0443}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0329}]}]}, {"inputVariants": 46, "outputVariants": 46, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 46, "outputVariants": 46, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1429}, {"inputVariants": 8, "outputVariants": 8, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.092}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0835}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0641}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1056}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0847}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0213}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0212}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.035500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.031100000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.0222}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0379}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.044500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.030000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0275}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.0206}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.020900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.0351}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0516}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.051300000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0504}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0546}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0814}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.09720000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0985}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0679}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0268}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0235}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0285}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0228}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029400000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}]}]}, {"inputVariants": 54, "outputVariants": 54, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 54, "outputVariants": 54, "pipeline": "", "variants": [{"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5295}, {"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3327}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0391}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.034300000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0408}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0499}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0216}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0212}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.034800000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0356}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0256}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0213}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038400000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027100000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028300000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0221}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0308}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0303}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045200000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0222}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027600000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.026000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.039900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0378}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0316}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.031400000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0267}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0327}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0519}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0297}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0285}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.030100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0281}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0223}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0275}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0516}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0456}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0322}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0592}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0315}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059500000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0734}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0325}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0218}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1119}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0634}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0955}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0796}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0896}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0955}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0758}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0505}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0221}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0297}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0279}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0212}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0279}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0427}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0205}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0228}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022000000000000002}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0212}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0258}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.020900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.048}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0206}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0453}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.021500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.021}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.021}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0327}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.0296}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.029300000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.0213}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0229}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0213}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0223}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.031200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0362}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Geometry)", "stripTimeMs": 0.0204}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VideoComposite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.052000000000000005}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/VideoDecode", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0674}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.042300000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0323}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031100000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0303}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0302}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10160000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0614}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033800000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.039}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Compositing", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0228}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0221}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0223}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032600000000000004}]}]}]}