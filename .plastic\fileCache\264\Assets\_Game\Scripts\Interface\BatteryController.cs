using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;

public class BatteryController : MonoBehaviour
{
    [SerializeField] private PlayerStatus playerStatus;

    // Preload the battery icons as assets instead of loading at runtime
    [Header("Battery Icons")]
    [SerializeField] private Texture2D emptyBatteryTexture;
    [SerializeField] private Texture2D lowBatteryTexture;
    [SerializeField] private Texture2D mediumBatteryTexture; 
    [SerializeField] private Texture2D fullBatteryTexture;

    private VisualElement batteryFill1; // Low section (0-15%)
    private VisualElement batteryFill2; // Medium section (15-50%)
    private VisualElement batteryFill3; // High section (50-100%)
    private VisualElement batteryIcon; // The battery icon element

    private UIDocument uiDocument;
    private bool isInitialized = false;
    private bool isUIVisible = false;

    [Header("Animation Settings")]
    [SerializeField, Range(50f, 300f)] private float fillAnimationSpeed = 50f; // Percentage points per second
    [SerializeField, Range(0.1f, 1f)] private float initialFillDelay = 0f; // Delay before starting the fill animation
    [SerializeField] private bool animateOnUIOpen = true; // Whether to animate when UI first opens
    [SerializeField] private bool skipAnimationOnFirstLaunch = true; // Skip animation on first UI open after game launch
    
    private float targetBatteryPercentage = 0f;
    private float currentBatteryPercentage = 0f;
    private float lastKnownEnergyPercentage = 0f; // Store last energy value even when UI is hidden
    private float animationSpeed = 100f; // Percentage points per second
    private Coroutine batteryAnimationCoroutine;
    private bool isFirstLaunch = true; // Track whether this is the first UI open since game launch

    // Initialize when the game starts
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
    private static void InitializeOnStartup()
    {
        // Ensure there's a battery controller in the scene
        EnsureBatteryController();
    }

    // Static method to ensure there's a battery controller in the scene
    public static BatteryController EnsureBatteryController()
    {
        // First try to find an existing controller
        var existingController = FindObjectOfType<BatteryController>();
        if (existingController != null)
        {
            return existingController;
        }   

        // Look for the InvUI component which should have a UIDocument
        var invUI = FindObjectOfType<InvUI>();
        if (invUI != null)
        {
            // Check if the InvUI GameObject already has a BatteryController
            var controller = invUI.GetComponent<BatteryController>();
            if (controller == null)
            {
                // If not, add one
                controller = invUI.gameObject.AddComponent<BatteryController>();
                Debug.Log("Added BatteryController to InvUI GameObject");
            }
            return controller;
        }

        // If no InvUI exists, create a new GameObject
        var newGameObject = new GameObject("BatteryController");
        var newController = newGameObject.AddComponent<BatteryController>();
        Debug.Log("Created new GameObject with BatteryController");
        return newController;
    }

    private void Awake()
    {
        uiDocument = GetComponent<UIDocument>();
        
        // Load textures if not already assigned in inspector
        LoadBatteryTextures();
        
        // Use the serialized animation speed
        animationSpeed = fillAnimationSpeed;
    }

    private void LoadBatteryTextures()
    {
        // Only load if not already assigned
        if (emptyBatteryTexture == null)
        {
            emptyBatteryTexture = Resources.Load<Texture2D>("UI/BatteryIcon_0");
            if (emptyBatteryTexture == null)
                Debug.LogError("Failed to load empty battery texture");
        }
        
        if (lowBatteryTexture == null)
        {
            lowBatteryTexture = Resources.Load<Texture2D>("UI/BatteryIcon_15");
            if (lowBatteryTexture == null)
                Debug.LogError("Failed to load low battery texture");
        }
        
        if (mediumBatteryTexture == null)
        {
            mediumBatteryTexture = Resources.Load<Texture2D>("UI/BatteryIcon_50");
            if (mediumBatteryTexture == null)
                Debug.LogError("Failed to load medium battery texture");
        }
        
        if (fullBatteryTexture == null)
        {
            fullBatteryTexture = Resources.Load<Texture2D>("UI/BatteryIcon_100");
            if (fullBatteryTexture == null)
                Debug.LogError("Failed to load full battery texture");
        }
    }

    private void Start()
    {
        // Find player status if not assigned
        if (playerStatus == null)
        {
            playerStatus = FindObjectOfType<PlayerStatus>();
        }

        if (playerStatus != null)
        {
            playerStatus.OnEnergyChanged += HandleEnergyChanged;
        }
        
        // Wait for UI document to be ready
        StartCoroutine(InitializeUIAfterDelay());
    }
    
    private IEnumerator InitializeUIAfterDelay()
    {
        // Wait a few frames to ensure UI document is fully loaded
        yield return new WaitForEndOfFrame();
        yield return new WaitForEndOfFrame();
        
        InitializeUI();
        
        // Set initial battery display
        if (playerStatus != null && isInitialized)
        {
            // Check if UI is visible
            bool isVisible = uiDocument.enabled;
            if (isVisible)
            {
                targetBatteryPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
                
                if (animateOnUIOpen)
                {
                    // Start with empty battery
                    currentBatteryPercentage = 0f;
                    UpdateBatteryDisplay(currentBatteryPercentage);
                    
                    // Animate to the current value
                    if (batteryAnimationCoroutine != null)
                        StopCoroutine(batteryAnimationCoroutine);
                    batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(initialFillDelay));
                }
                else
                {
                    // No animation, just set directly
                    currentBatteryPercentage = targetBatteryPercentage;
                    UpdateBatteryDisplay(currentBatteryPercentage);
                }
            }
        }
    }

    private void OnDestroy()
    {
        if (playerStatus != null)
        {
            playerStatus.OnEnergyChanged -= HandleEnergyChanged;
        }
    }

    private void InitializeUI()
    {
        if (uiDocument == null)
        {
            Debug.LogError("UIDocument is null! Can't find battery elements.");
            return;
        }

        var root = uiDocument.rootVisualElement;
        
        if (root == null)
        {
            Debug.LogError("Root visual element is null!");
            return;
        }

        // First find the EnergyContainer 
        var energyContainer = root.Q<VisualElement>("EnergyContainer");
        if (energyContainer == null)
        {
            Debug.LogError("EnergyContainer not found in UI hierarchy!");
            return;
        }

        // Then the BatteryIndicator
        var batteryIndicator = energyContainer.Q<VisualElement>("BatteryIndicator");
        if (batteryIndicator == null)
        {
            Debug.LogError("BatteryIndicator not found in UI hierarchy!");
            return;
        }

        // Get the battery icon
        batteryIcon = batteryIndicator.Q<VisualElement>("BatteryIcon");
        if (batteryIcon == null)
        {
            Debug.LogError("BatteryIcon not found in UI hierarchy!");
            return;
        }

        // Then the BatteryBar
        var batteryBar = batteryIndicator.Q<VisualElement>("BatteryBar");
        if (batteryBar == null)
        {
            Debug.LogError("BatteryBar not found in UI hierarchy!");
            return;
        }

        // Finally get the fill elements
        batteryFill1 = batteryBar.Q<VisualElement>("BatteryFill1");
        batteryFill2 = batteryBar.Q<VisualElement>("BatteryFill2");
        batteryFill3 = batteryBar.Q<VisualElement>("BatteryFill3");

        if (batteryFill1 == null || batteryFill2 == null || batteryFill3 == null)
        {
            Debug.LogError($"Battery fill elements not found! Fill1: {batteryFill1 != null}, Fill2: {batteryFill2 != null}, Fill3: {batteryFill3 != null}");
            return;
        }

        // Make sure battery fills are visible by explicitly setting display style
        batteryFill1.style.display = DisplayStyle.Flex;
        batteryFill2.style.display = DisplayStyle.Flex;
        batteryFill3.style.display = DisplayStyle.Flex;

        Debug.Log("Successfully found all battery UI elements!");
        isInitialized = true;
        
        // Automatically detect if UI is visible based on UIDocument enabled state
        SetUIVisibility(uiDocument.enabled && IsUIElementVisible(energyContainer));

        // Monitor for UI document enabled state changes
        StartCoroutine(MonitorUIVisibility());
    }

    // Check if a UI element is actually visible in the hierarchy
    private bool IsUIElementVisible(VisualElement element)
    {
        if (element == null) return false;
        
        // Check if this element is visible
        if (element.style.display == DisplayStyle.None)
            return false;
            
        if (element.resolvedStyle.opacity <= 0.01f) // Effectively invisible
            return false;
            
        // An element with zero width or height is not visible
        if (element.resolvedStyle.width <= 0 || element.resolvedStyle.height <= 0)
            return false;
            
        return true;
    }

    // Monitor for UI panel visibility changes
    private IEnumerator MonitorUIVisibility()
    {
        bool previousState = isUIVisible;
        
        while (true)
        {
            // Check if UIDocument is enabled
            bool currentVisibility = false;
            
            if (uiDocument != null && uiDocument.enabled && uiDocument.rootVisualElement != null)
            {
                var energyContainer = uiDocument.rootVisualElement.Q<VisualElement>("EnergyContainer");
                currentVisibility = IsUIElementVisible(energyContainer);
            }
            
            // If visibility state changed
            if (currentVisibility != previousState)
            {
                SetUIVisibility(currentVisibility);
                previousState = currentVisibility;
            }
            
            // Check every 0.2 seconds to avoid unnecessary performance impact
            yield return new WaitForSeconds(0.2f);
        }
    }

    private void HandleEnergyChanged(float oldEnergy, float newEnergy)
    {
        // Always update the last known energy percentage, even if UI is not visible
        lastKnownEnergyPercentage = (newEnergy / playerStatus.maxEnergy) * 100f;
        
        // Only update visual display if UI is visible
        if (!isUIVisible) return;
        
        Debug.Log($"Energy changed: {oldEnergy} -> {newEnergy}, percentage: {(newEnergy / playerStatus.maxEnergy) * 100f}%");
        
        // Keep current value (don't reset to 0)
        // Just update target for animation
        targetBatteryPercentage = lastKnownEnergyPercentage;
        
        // Start smooth animation from current to new value
        if (batteryAnimationCoroutine != null)
            StopCoroutine(batteryAnimationCoroutine);
        batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(0f));
    }

    // Check if UI is visible and needs updates
    public void SetUIVisibility(bool isVisible)
    {
        if (isUIVisible != isVisible)
        {
            isUIVisible = isVisible;
            
            // If becoming visible, update the display
            if (isUIVisible && playerStatus != null && isInitialized)
            {
                // Set the target percentage to current energy
                targetBatteryPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
                
                // For initial display, don't animate - just show current value
                currentBatteryPercentage = targetBatteryPercentage;
                UpdateBatteryDisplay(currentBatteryPercentage);
            }
        }
    }
    
    // Coroutine to animate the battery fill from previous to current value
    private IEnumerator AnimateBatteryFill(float delay)
    {
        // Optional delay before starting animation
        if (delay > 0)
            yield return new WaitForSeconds(delay);
        
        // Store the starting value
        float startValue = currentBatteryPercentage;
        
        // Animate until we reach target
        while (Mathf.Abs(currentBatteryPercentage - targetBatteryPercentage) > 0.1f)
        {
            currentBatteryPercentage = Mathf.MoveTowards(
                currentBatteryPercentage, 
                targetBatteryPercentage, 
                animationSpeed * Time.deltaTime
            );
            
            UpdateBatteryDisplay(currentBatteryPercentage);
            yield return null;
        }
        
        // Final update to ensure we hit the exact target
        currentBatteryPercentage = targetBatteryPercentage;
        UpdateBatteryDisplay(currentBatteryPercentage);
        
        batteryAnimationCoroutine = null;
    }

    // Update only when necessary
    private void Update()
    {
        // If UI is not visible, don't update
        if (!isUIVisible)
            return;

        // Initialize if needed
        if (!isInitialized && uiDocument != null && uiDocument.rootVisualElement != null)
        {
            InitializeUI();
            if (!isInitialized)
                return;
        }
         
        // Test controls for energy manipulation 
        if (Input.GetKeyDown(KeyCode.KeypadPlus) || Input.GetKeyDown(KeyCode.Equals))
        {
            playerStatus.RestoreEnergy(10f);
            Debug.Log($"Added energy: {playerStatus.currentEnergy}/{playerStatus.maxEnergy}");
            // Update target for animation - keep current value and animate to new
            targetBatteryPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
            
            // Start smooth animation from current to new value
            if (batteryAnimationCoroutine != null)
                StopCoroutine(batteryAnimationCoroutine);
            batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(0f));
        }
        else if (Input.GetKeyDown(KeyCode.KeypadMinus) || Input.GetKeyDown(KeyCode.Minus))
        {
            playerStatus.currentEnergy -= 10f;
            playerStatus.currentEnergy = Mathf.Max(0f, playerStatus.currentEnergy);
            Debug.Log($"Reduced energy: {playerStatus.currentEnergy}/{playerStatus.maxEnergy}");
            // Update target for animation - keep current value and animate to new
            targetBatteryPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
            
            // Start smooth animation from current to new value
            if (batteryAnimationCoroutine != null)
                StopCoroutine(batteryAnimationCoroutine);
            batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(0f));
        }
        
        // Force reset the battery display
        if (Input.GetKeyDown(KeyCode.B))
        {
            Debug.Log("Forcing battery display reset");
            ResetBatteryDisplay();
        }
    }

    private void ResetBatteryDisplay()
    {
        // Reset all display elements
        if (batteryFill1 != null)
        {
            batteryFill1.style.display = DisplayStyle.Flex;
            batteryFill1.style.width = new StyleLength(new Length(15, LengthUnit.Percent));
            Debug.Log($"Reset batteryFill1: Display={batteryFill1.style.display}, Width={batteryFill1.style.width}");
        }
        
        if (batteryFill2 != null)
        {
            batteryFill2.style.display = DisplayStyle.Flex;
            batteryFill2.style.width = new StyleLength(new Length(35, LengthUnit.Percent));
            Debug.Log($"Reset batteryFill2: Display={batteryFill2.style.display}, Width={batteryFill2.style.width}");
        }
        
        if (batteryFill3 != null)
        {
            batteryFill3.style.display = DisplayStyle.Flex;
            batteryFill3.style.width = new StyleLength(new Length(50, LengthUnit.Percent));
            Debug.Log($"Reset batteryFill3: Display={batteryFill3.style.display}, Width={batteryFill3.style.width}");
        }
        
        // Reset the battery icon
        if (batteryIcon != null)
        {
            batteryIcon.style.backgroundImage = StyleKeyword.None;
            Debug.Log("Reset battery icon");
        }
        
        // Re-setup elements
        InitializeUI();
        
        // Update display with animation
        if (playerStatus != null)
        {
            // Set both current and target to current energy
            currentBatteryPercentage = targetBatteryPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
            
            // Update immediately without animation
            UpdateBatteryDisplay(currentBatteryPercentage);
            
            Debug.Log($"Battery display reset with energy: {targetBatteryPercentage}%");
        }
    }
    
    /// <summary>
    /// Sets the battery percentage and updates the display
    /// </summary>
    /// <param name="percentage">Battery percentage (0-100)</param>
    public void SetBatteryPercentage(float percentage)
    {
        if (!isInitialized || !isUIVisible)
            return;
            
        float batteryPercentage = Mathf.Clamp(percentage, 0f, 100f);
        // Set target for animation
        targetBatteryPercentage = batteryPercentage;
        
        // Start animation from current to new value
        if (batteryAnimationCoroutine != null)
            StopCoroutine(batteryAnimationCoroutine);
        batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(0f));
    }

    /// <summary>
    /// Updates the battery fill display based on current percentage
    /// </summary>
    private void UpdateBatteryDisplay(float percentage)
    {
        if (batteryFill1 == null || batteryFill2 == null || batteryFill3 == null || batteryIcon == null)
        {
            Debug.LogError("One or more battery UI elements not found! BatteryFill1: " + 
                          (batteryFill1 != null) + ", BatteryFill2: " + 
                          (batteryFill2 != null) + ", BatteryFill3: " + 
                          (batteryFill3 != null) + ", BatteryIcon: " +
                          (batteryIcon != null));
            return;
        }

        // Update battery icon based on percentage
        UpdateBatteryIcon(percentage);
        
        // Control which sections should be visible
        if (percentage <= 0)
        {
            // Empty battery - all sections hidden
            batteryFill1.style.display = DisplayStyle.None;
            batteryFill2.style.display = DisplayStyle.None;
            batteryFill3.style.display = DisplayStyle.None;
        }
        else if (percentage <= 15)
        {
            // Only show the first section, with partial width
            float fillWidth = percentage / 15f * 15f; // Calculate percentage of the 15% section
            batteryFill1.style.width = new StyleLength(new Length(fillWidth, LengthUnit.Percent));
            batteryFill1.style.display = DisplayStyle.Flex;
            
            // Hide other sections
            batteryFill2.style.display = DisplayStyle.None;
            batteryFill3.style.display = DisplayStyle.None;
        }
        else if (percentage <= 50)
        {
            // First section is fully visible
            batteryFill1.style.width = new StyleLength(new Length(15, LengthUnit.Percent));
            batteryFill1.style.display = DisplayStyle.Flex;
            
            // Second section is partially visible
            float sectionPercentage = (percentage - 15) / 35f; // How much of the middle section to show (0-1)
            float fillWidth = sectionPercentage * 35f; // Actual width percentage (0-35%)
            batteryFill2.style.width = new StyleLength(new Length(fillWidth, LengthUnit.Percent));
            batteryFill2.style.display = DisplayStyle.Flex;
            
            // Third section is hidden
            batteryFill3.style.display = DisplayStyle.None;
        }
        else
        {
            // First two sections are fully visible
            batteryFill1.style.width = new StyleLength(new Length(15, LengthUnit.Percent));
            batteryFill1.style.display = DisplayStyle.Flex;
            
            batteryFill2.style.width = new StyleLength(new Length(35, LengthUnit.Percent));
            batteryFill2.style.display = DisplayStyle.Flex;
            
            // Third section is partially visible
            float sectionPercentage = (percentage - 50) / 50f; // How much of the last section to show (0-1)
            float fillWidth = sectionPercentage * 50f; // Actual width percentage (0-50%)
            batteryFill3.style.width = new StyleLength(new Length(fillWidth, LengthUnit.Percent));
            batteryFill3.style.display = DisplayStyle.Flex;
        }
    }
    
    /// <summary>
    /// Updates the battery icon based on the energy percentage
    /// </summary>
    private void UpdateBatteryIcon(float percentage)
    {
        if (batteryIcon == null)
        {
            Debug.LogError("Battery icon is null!");
            return;
        }
    
        // Choose the appropriate texture based on the percentage
        Texture2D iconTexture = null;
        
        if (percentage <= 0 && emptyBatteryTexture != null)
        {
            iconTexture = emptyBatteryTexture;
        }
        else if (percentage <= 15 && lowBatteryTexture != null)
        {
            iconTexture = lowBatteryTexture;
        }
        else if (percentage <= 50 && mediumBatteryTexture != null)
        {
            iconTexture = mediumBatteryTexture;
        }
        else if (fullBatteryTexture != null)
        {
            iconTexture = fullBatteryTexture;
        }
        
        // Apply the texture if it's valid
        if (iconTexture != null)
        {
            batteryIcon.style.backgroundImage = iconTexture;
        }
        else
        {
            Debug.LogWarning($"No valid battery texture found for percentage: {percentage}");
        }
    }

    // Public method to notify this controller when inventory is opened via Tab
    public void OnInventoryOpened()
    {
        if (isInitialized && playerStatus != null)
        {
            // Calculate current energy percentage
            float currentEnergyPercentage = (playerStatus.currentEnergy / playerStatus.maxEnergy) * 100f;
            
            // Update last known percentage
            lastKnownEnergyPercentage = currentEnergyPercentage;
            
            // Set the UI to visible first
            isUIVisible = true;
            
            // Get the percentage difference between current display and actual energy
            // If the battery has changed significantly while UI was closed, animate the change
            if (Mathf.Abs(currentBatteryPercentage - currentEnergyPercentage) > 5f && animateOnUIOpen)
            {
                // Skip animation on first launch if requested
                if (isFirstLaunch && skipAnimationOnFirstLaunch)
                {
                    // On first launch, just set values directly without animation
                    currentBatteryPercentage = currentEnergyPercentage;
                    targetBatteryPercentage = currentEnergyPercentage;
                    UpdateBatteryDisplay(currentBatteryPercentage);
                    isFirstLaunch = false;
                    return;
                }
                
                // If we haven't displayed anything yet or display is at 0
                if (currentBatteryPercentage <= 0.1f)
                {
                    // If we're opening for the first time, start at a non-zero value that's not the target
                    float startingPercentage = currentEnergyPercentage * 0.25f; // 25% of actual value
                    currentBatteryPercentage = Mathf.Max(5f, startingPercentage); // At least 5%
                }
                
                // Set target to actual energy
                targetBatteryPercentage = currentEnergyPercentage;
                
                // Start animation from current to new value with delay
                if (batteryAnimationCoroutine != null)
                    StopCoroutine(batteryAnimationCoroutine);
                batteryAnimationCoroutine = StartCoroutine(AnimateBatteryFill(initialFillDelay));
                
                // Show initial state before animation
                UpdateBatteryDisplay(currentBatteryPercentage);
                
                // No longer first launch
                isFirstLaunch = false;
            }
            else
            {
                // No animation needed - just show current value
                currentBatteryPercentage = currentEnergyPercentage;
                targetBatteryPercentage = currentEnergyPercentage;
                UpdateBatteryDisplay(currentBatteryPercentage);
                isFirstLaunch = false;
            }
        }
    }
} 