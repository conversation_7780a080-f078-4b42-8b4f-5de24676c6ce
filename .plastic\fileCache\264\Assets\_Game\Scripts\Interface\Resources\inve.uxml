<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="project://database/Assets/_Game/Scripts/Interface/Resources/inve.uss?fileID=7433441132597879392&amp;guid=42f6485d91c876f4b926c7584655603d&amp;type=3#inve" />
    <ui:VisualElement name="RootContainer" class="root-container">
        <ui:VisualElement name="StashMainContainer" class="main-container">
            <ui:VisualElement name="StashContainer" class="panel panel--primary">
                <ui:VisualElement name="Header" class="header">
                    <ui:VisualElement class="header__icon" style="background-image: resource(&apos;UI/bolt&apos;);" />
                    <ui:Label text="STASH" display-tooltip-when-elided="true" name="StashTitle" class="header__title" />
                    <ui:VisualElement class="header__icon" style="background-image: resource(&apos;UI/bolt&apos;);" />
                </ui:VisualElement>
                <ui:ScrollView name="StashScrollView" mode="VerticalAndHorizontal" vertical-scroller-visibility="Hidden" horizontal-scroller-visibility="Hidden" class="scroll-view">
                    <ui:VisualElement name="StashGrid" class="grid-container" />
                </ui:ScrollView>
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement name="InvMainContainer" class="main-container">
            <ui:VisualElement name="InvContainer" id="InvContainer" class="panel panel--primary">
                <ui:VisualElement name="Header" class="header">
                    <ui:VisualElement class="header__icon" style="background-image: resource(&apos;UI/bolt&apos;);" />
                    <ui:Label text="€ 000000000" display-tooltip-when-elided="true" name="CurrencyLabel" class="header__money" />
                    <ui:VisualElement class="header__icon" style="background-image: resource(&apos;UI/bolt&apos;);" />
                </ui:VisualElement>
                <ui:VisualElement name="EquipmentArea" class="equipment-area">
                    <ui:VisualElement name="EquipmentSlots" class="equipment-slots">
                        <ui:VisualElement name="HeadSlot" id="HeadSlot" class="equipment-slot empty" style="width: 124px; height: 124px; min-width: 124px; min-height: 124px; max-height: 124px; max-width: 124px; margin-right: 8px; align-items: center; justify-content: center;">
                            <ui:Image name="HeadSlotIcon" id="HeadSlotIcon" class="equipment-slot-img" style="background-image: url(&quot;project://database/Assets/_Game/Resources/UI/Placeholder_Helmet.png?fileID=21300000&amp;guid=dec077adde63d60419133ee65217fc44&amp;type=3#Placeholder_Helmet&quot;);" />
                        </ui:VisualElement>
                        <ui:VisualElement name="ChestSlot" id="ChestSlot" class="equipment-slot empty" style="width: 124px; height: 124px; min-height: 124px; min-width: 124px; max-width: 124px; max-height: 124px; margin-right: 8px; align-items: center; justify-content: center;">
                            <ui:Image name="ChestSlotIcon" id="ChestSlotIcon" class="equipment-slot-img" style="background-image: url(&quot;project://database/Assets/_Game/Resources/UI/Placeholder_Chest.png?fileID=21300000&amp;guid=cfe99bfd62c310048889033c2aaf6c93&amp;type=3#Placeholder_Chest&quot;);" />
                        </ui:VisualElement>
                        <ui:VisualElement name="BagSlot" id="BagSlot" class="equipment-slot empty" style="max-width: 124px; min-width: 124px; min-height: 124px; max-height: 124px; height: 124px; width: 124px; align-items: center; justify-content: center;">
                            <ui:Image name="BagSlotIcon" id="BagSlotIcon" class="equipment-slot-img" style="background-image: url(&quot;project://database/Assets/_Game/Resources/UI/Placeholder_Bag.png?fileID=21300000&amp;guid=74743076af2b40a4c8f093981a264bdc&amp;type=3#Placeholder_Bag&quot;);" />
                        </ui:VisualElement>
                    </ui:VisualElement>
                </ui:VisualElement>
                <ui:ScrollView name="Inv" mode="Vertical" class="scroll-view">
                    <ui:VisualElement name="InvPlaceholder" class="inventory-placeholder">
                        <ui:Label text="Equip a bag to access inventory storage" class="inventory-placeholder-text" />
                    </ui:VisualElement>
                    <ui:VisualElement name="InvGrid" class="grid-container" />
                </ui:ScrollView>
                <ui:VisualElement name="Stats" class="stats" style="display: none;">
                    <ui:Label tabindex="-1" text="0/0" display-tooltip-when-elided="true" name="WeightLabel" enable-rich-text="false" class="stats__label stats__weight" />
                    <ui:Label tabindex="-1" text="0/100" display-tooltip-when-elided="true" name="EnergyLabel" class="stats__label stats__energy" />
                </ui:VisualElement>
                <ui:VisualElement name="EnergyContainer" class="energy-container">
                    <ui:VisualElement name="BatteryIndicator" class="battery-indicator">
                        <ui:VisualElement name="BatteryIcon" class="battery-icon" />
                        <ui:VisualElement name="BatteryBar" class="battery-bar">
                            <ui:VisualElement name="BatteryFill1" class="battery-fill battery-fill--low" style="width: 15%; left: 0; display: flex;" />
                            <ui:VisualElement name="BatteryFill2" class="battery-fill battery-fill--medium" style="width: 35%; left: 15%; display: flex;" />
                            <ui:VisualElement name="BatteryFill3" class="battery-fill battery-fill--high" style="width: 50%; left: 50%; display: flex;" />
                        </ui:VisualElement>
                    </ui:VisualElement>
                </ui:VisualElement>
            </ui:VisualElement>
            <ui:VisualElement name="ShopContainer" class="panel panel--secondary">
                <ui:VisualElement name="Header" class="header">
                    <ui:VisualElement class="header__icon" style="background-image: resource(&apos;UI/bolt&apos;);" />
                    <ui:Label text="TRADER" display-tooltip-when-elided="true" name="ShopTitle" class="header__title" />
                    <ui:VisualElement class="header__icon" style="background-image: resource(&apos;UI/bolt&apos;);" />
                </ui:VisualElement>
                <ui:VisualElement name="ShopModeControls" class="shop-controls">
                    <ui:Button text="Buy" name="BuyModeButton" class="pixel-button pixel-button--mode pixel-button--active" />
                    <ui:Button text="Sell" name="SellModeButton" class="pixel-button pixel-button--mode" />
                </ui:VisualElement>
                <ui:VisualElement name="BuyModeContainer" class="mode-container">
                    <ui:ScrollView name="ShopScrollView" vertical-scroller-visibility="Auto" horizontal-scroller-visibility="Hidden" class="scroll-view">
                        <ui:VisualElement name="ShopGrid" class="grid-container" />
                    </ui:ScrollView>
                    <ui:VisualElement name="ShopFooter" class="shop-footer">
                        <ui:VisualElement name="ValueInfo" class="value-info">
                            <ui:Label text="€ 0" name="BuyTotalValue" class="label-value" style="color: rgb(153, 153, 153);" />
                        </ui:VisualElement>
                        <ui:Button text="Buy" name="BuyButton" enabled="false" class="pixel-button pixel-button--buy disabled" />
                    </ui:VisualElement>
                </ui:VisualElement>
                <ui:VisualElement name="SellModeContainer" class="mode-container" style="display: none;">
                    <ui:ScrollView name="SellGridScrollView" vertical-scroller-visibility="Auto" horizontal-scroller-visibility="Hidden" mode="VerticalAndHorizontal" class="scroll-view" style="padding-top: 0; padding-right: 0; padding-bottom: 0; padding-left: 0;">
                        <ui:VisualElement name="SellGrid" class="grid-container" />
                    </ui:ScrollView>
                    <ui:VisualElement name="SellFooter" class="shop-footer">
                        <ui:VisualElement name="ValueInfo" class="value-info">
                            <ui:Label text="€ 0" name="SellTotalValue" class="label-value" style="color: rgb(153, 153, 153);" />
                        </ui:VisualElement>
                        <ui:Button text="Sell" name="SellButton" enabled="false" class="pixel-button pixel-button--sell disabled" />
                    </ui:VisualElement>
                </ui:VisualElement>
                <ui:VisualElement name="InstructionsContainer" class="instructions-container" style="height: 40px;">
                    <ui:VisualElement name="BuyInstructionsContainer" class="instructions-wrapper">
                        <ui:Label text="Click any item/s to select to buy" display-tooltip-when-elided="true" name="BuyInstructionText" class="instruction-text" />
                    </ui:VisualElement>
                    <ui:VisualElement name="SellInstructionsContainer" class="instructions-wrapper" style="display: none;">
                        <ui:Label text="Click any item/s to select to sell" display-tooltip-when-elided="true" name="SellInstructionText" class="instruction-text" />
                    </ui:VisualElement>
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>