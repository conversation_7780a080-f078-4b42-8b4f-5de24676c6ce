{ "pid": 93864, "tid": 73014444032, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928456974, "dur": 25658, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928482633, "dur": 314619, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928482646, "dur": 40, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928482690, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928482692, "dur": 531, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928483229, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928483268, "dur": 6, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928483275, "dur": 3434, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486715, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486767, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486769, "dur": 33, "ph": "X", "name": "ReadAsync 561", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486806, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486808, "dur": 36, "ph": "X", "name": "ReadAsync 544", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486850, "dur": 33, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486886, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486888, "dur": 34, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486924, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486926, "dur": 31, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486959, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486961, "dur": 33, "ph": "X", "name": "ReadAsync 466", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928486997, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487000, "dur": 33, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487035, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487036, "dur": 26, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487066, "dur": 23, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487091, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487116, "dur": 30, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487150, "dur": 18, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487171, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487211, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487231, "dur": 17, "ph": "X", "name": "ReadAsync 235", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487250, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487268, "dur": 15, "ph": "X", "name": "ReadAsync 65", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487285, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487304, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487328, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487352, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487354, "dur": 87, "ph": "X", "name": "ReadAsync 134", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487443, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487467, "dur": 18, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487487, "dur": 16, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487505, "dur": 16, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487524, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487544, "dur": 16, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487563, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487584, "dur": 18, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487604, "dur": 18, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487624, "dur": 28, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487654, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487676, "dur": 17, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487695, "dur": 29, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487727, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487728, "dur": 32, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487764, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487766, "dur": 23, "ph": "X", "name": "ReadAsync 548", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487792, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487815, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487817, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487843, "dur": 30, "ph": "X", "name": "ReadAsync 533", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487876, "dur": 20, "ph": "X", "name": "ReadAsync 491", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487900, "dur": 17, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487918, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487937, "dur": 16, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487955, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487979, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928487997, "dur": 20, "ph": "X", "name": "ReadAsync 257", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488020, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488038, "dur": 17, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488058, "dur": 17, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488077, "dur": 17, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488095, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488115, "dur": 12, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488130, "dur": 16, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488148, "dur": 17, "ph": "X", "name": "ReadAsync 189", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488167, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488187, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488207, "dur": 16, "ph": "X", "name": "ReadAsync 463", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488225, "dur": 12, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488239, "dur": 34, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488276, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488306, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488326, "dur": 18, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488346, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488369, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488390, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488411, "dur": 17, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488430, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488449, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488469, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488490, "dur": 16, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488508, "dur": 18, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488528, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488550, "dur": 16, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488568, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488587, "dur": 18, "ph": "X", "name": "ReadAsync 220", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488607, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488632, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488653, "dur": 17, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488672, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488690, "dur": 17, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488709, "dur": 17, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488728, "dur": 18, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488748, "dur": 18, "ph": "X", "name": "ReadAsync 428", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488768, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488787, "dur": 16, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488805, "dur": 16, "ph": "X", "name": "ReadAsync 458", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488823, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488841, "dur": 16, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488859, "dur": 16, "ph": "X", "name": "ReadAsync 227", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488877, "dur": 18, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488897, "dur": 16, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488915, "dur": 17, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488935, "dur": 15, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488952, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488973, "dur": 17, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928488992, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489012, "dur": 17, "ph": "X", "name": "ReadAsync 559", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489032, "dur": 24, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489057, "dur": 19, "ph": "X", "name": "ReadAsync 548", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489079, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489096, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489117, "dur": 15, "ph": "X", "name": "ReadAsync 409", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489134, "dur": 16, "ph": "X", "name": "ReadAsync 157", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489153, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489171, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489190, "dur": 17, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489210, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489233, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489256, "dur": 26, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489285, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489304, "dur": 18, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489324, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489346, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489367, "dur": 17, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489387, "dur": 21, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489411, "dur": 17, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489430, "dur": 12, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489444, "dur": 16, "ph": "X", "name": "ReadAsync 435", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489462, "dur": 15, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489479, "dur": 23, "ph": "X", "name": "ReadAsync 85", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489504, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489527, "dur": 18, "ph": "X", "name": "ReadAsync 523", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489548, "dur": 21, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489571, "dur": 24, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489597, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489619, "dur": 16, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489637, "dur": 18, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489657, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489677, "dur": 17, "ph": "X", "name": "ReadAsync 534", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489696, "dur": 17, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489715, "dur": 26, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489743, "dur": 18, "ph": "X", "name": "ReadAsync 522", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489763, "dur": 15, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489781, "dur": 16, "ph": "X", "name": "ReadAsync 61", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489799, "dur": 16, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489817, "dur": 18, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489837, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489855, "dur": 16, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489874, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489892, "dur": 18, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489912, "dur": 16, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489931, "dur": 20, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489954, "dur": 23, "ph": "X", "name": "ReadAsync 485", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489979, "dur": 17, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928489998, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490021, "dur": 19, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490042, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490064, "dur": 17, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490083, "dur": 20, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490105, "dur": 20, "ph": "X", "name": "ReadAsync 469", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490127, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490150, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490173, "dur": 16, "ph": "X", "name": "ReadAsync 202", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490192, "dur": 16, "ph": "X", "name": "ReadAsync 174", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490210, "dur": 20, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490233, "dur": 15, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490249, "dur": 16, "ph": "X", "name": "ReadAsync 188", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490267, "dur": 15, "ph": "X", "name": "ReadAsync 209", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490285, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490301, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490317, "dur": 49, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490369, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490392, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490411, "dur": 43, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490456, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490459, "dur": 16, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490478, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490498, "dur": 16, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490515, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490539, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490558, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490574, "dur": 18, "ph": "X", "name": "ReadAsync 191", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490595, "dur": 16, "ph": "X", "name": "ReadAsync 255", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490613, "dur": 16, "ph": "X", "name": "ReadAsync 264", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490632, "dur": 16, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490650, "dur": 14, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490667, "dur": 17, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490686, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490705, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490724, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490743, "dur": 15, "ph": "X", "name": "ReadAsync 266", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490761, "dur": 16, "ph": "X", "name": "ReadAsync 183", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490778, "dur": 12, "ph": "X", "name": "ReadAsync 214", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490793, "dur": 16, "ph": "X", "name": "ReadAsync 256", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490811, "dur": 19, "ph": "X", "name": "ReadAsync 234", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490832, "dur": 17, "ph": "X", "name": "ReadAsync 133", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490851, "dur": 16, "ph": "X", "name": "ReadAsync 256", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490869, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490886, "dur": 16, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490905, "dur": 56, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490962, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490964, "dur": 32, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490998, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928490999, "dur": 21, "ph": "X", "name": "ReadAsync 871", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491022, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491046, "dur": 28, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491076, "dur": 21, "ph": "X", "name": "ReadAsync 584", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491099, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491120, "dur": 21, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491143, "dur": 27, "ph": "X", "name": "ReadAsync 545", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491171, "dur": 16, "ph": "X", "name": "ReadAsync 628", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491190, "dur": 19, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491211, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491232, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491252, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491275, "dur": 20, "ph": "X", "name": "ReadAsync 574", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491298, "dur": 25, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491325, "dur": 15, "ph": "X", "name": "ReadAsync 588", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491342, "dur": 16, "ph": "X", "name": "ReadAsync 178", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491360, "dur": 15, "ph": "X", "name": "ReadAsync 194", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491376, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491394, "dur": 16, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491411, "dur": 15, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491428, "dur": 19, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491449, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491467, "dur": 16, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491485, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491504, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491524, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491542, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491560, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491578, "dur": 17, "ph": "X", "name": "ReadAsync 393", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491597, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491615, "dur": 16, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491633, "dur": 16, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491651, "dur": 16, "ph": "X", "name": "ReadAsync 231", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491669, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491688, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491705, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491723, "dur": 17, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491742, "dur": 16, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491760, "dur": 50, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491812, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491834, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491854, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491873, "dur": 18, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491893, "dur": 16, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491912, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491930, "dur": 15, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491948, "dur": 15, "ph": "X", "name": "ReadAsync 186", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491964, "dur": 17, "ph": "X", "name": "ReadAsync 193", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928491983, "dur": 16, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492000, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492019, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492037, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492056, "dur": 16, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492073, "dur": 14, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492090, "dur": 16, "ph": "X", "name": "ReadAsync 99", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492108, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492127, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492143, "dur": 16, "ph": "X", "name": "ReadAsync 238", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492161, "dur": 16, "ph": "X", "name": "ReadAsync 186", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492180, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492196, "dur": 14, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492213, "dur": 15, "ph": "X", "name": "ReadAsync 191", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492231, "dur": 15, "ph": "X", "name": "ReadAsync 213", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492248, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492266, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492283, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492301, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492318, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492336, "dur": 17, "ph": "X", "name": "ReadAsync 252", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492355, "dur": 15, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492372, "dur": 15, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492388, "dur": 184, "ph": "X", "name": "ReadAsync 257", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492574, "dur": 1, "ph": "X", "name": "ProcessMessages 2784", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492576, "dur": 17, "ph": "X", "name": "ReadAsync 2784", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492595, "dur": 22, "ph": "X", "name": "ReadAsync 463", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492618, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492640, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492661, "dur": 19, "ph": "X", "name": "ReadAsync 534", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492682, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492703, "dur": 22, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492728, "dur": 15, "ph": "X", "name": "ReadAsync 522", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492746, "dur": 15, "ph": "X", "name": "ReadAsync 169", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492763, "dur": 16, "ph": "X", "name": "ReadAsync 305", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492780, "dur": 15, "ph": "X", "name": "ReadAsync 204", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492797, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492816, "dur": 15, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492833, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492852, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492871, "dur": 16, "ph": "X", "name": "ReadAsync 228", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492890, "dur": 16, "ph": "X", "name": "ReadAsync 183", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492908, "dur": 16, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492925, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492927, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492944, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492961, "dur": 17, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492980, "dur": 17, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928492999, "dur": 14, "ph": "X", "name": "ReadAsync 263", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493016, "dur": 16, "ph": "X", "name": "ReadAsync 103", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493033, "dur": 15, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493051, "dur": 16, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493069, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493088, "dur": 15, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493104, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493122, "dur": 15, "ph": "X", "name": "ReadAsync 258", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493139, "dur": 22, "ph": "X", "name": "ReadAsync 235", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493163, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493181, "dur": 15, "ph": "X", "name": "ReadAsync 149", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493198, "dur": 12, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493212, "dur": 29, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493245, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493267, "dur": 16, "ph": "X", "name": "ReadAsync 377", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493285, "dur": 16, "ph": "X", "name": "ReadAsync 150", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493303, "dur": 16, "ph": "X", "name": "ReadAsync 218", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493321, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493339, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493358, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493376, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493394, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493412, "dur": 15, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493428, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493430, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493451, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493471, "dur": 15, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493489, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493506, "dur": 16, "ph": "X", "name": "ReadAsync 202", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493524, "dur": 17, "ph": "X", "name": "ReadAsync 227", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493542, "dur": 15, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493559, "dur": 11, "ph": "X", "name": "ReadAsync 140", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493572, "dur": 16, "ph": "X", "name": "ReadAsync 217", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493590, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493613, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493632, "dur": 14, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493647, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493664, "dur": 16, "ph": "X", "name": "ReadAsync 207", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493681, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493699, "dur": 15, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493716, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493734, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493751, "dur": 16, "ph": "X", "name": "ReadAsync 256", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493770, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493791, "dur": 15, "ph": "X", "name": "ReadAsync 258", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493808, "dur": 16, "ph": "X", "name": "ReadAsync 284", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493826, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493844, "dur": 14, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493859, "dur": 16, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493878, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493907, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493925, "dur": 16, "ph": "X", "name": "ReadAsync 447", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493943, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493960, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493980, "dur": 16, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928493998, "dur": 17, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494017, "dur": 15, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494034, "dur": 16, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494053, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494071, "dur": 15, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494088, "dur": 14, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494105, "dur": 15, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494122, "dur": 15, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494139, "dur": 18, "ph": "X", "name": "ReadAsync 241", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494159, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494181, "dur": 18, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494201, "dur": 15, "ph": "X", "name": "ReadAsync 231", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494219, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494239, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494263, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494282, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494303, "dur": 27, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494332, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494353, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494371, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494389, "dur": 18, "ph": "X", "name": "ReadAsync 204", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494409, "dur": 15, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494426, "dur": 14, "ph": "X", "name": "ReadAsync 196", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494442, "dur": 16, "ph": "X", "name": "ReadAsync 152", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494461, "dur": 16, "ph": "X", "name": "ReadAsync 266", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494478, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494497, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494516, "dur": 16, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494534, "dur": 16, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494552, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494569, "dur": 11, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494583, "dur": 18, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494603, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494624, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494642, "dur": 16, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494660, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494680, "dur": 17, "ph": "X", "name": "ReadAsync 249", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494699, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494716, "dur": 15, "ph": "X", "name": "ReadAsync 199", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494733, "dur": 17, "ph": "X", "name": "ReadAsync 146", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494752, "dur": 16, "ph": "X", "name": "ReadAsync 218", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494770, "dur": 16, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494788, "dur": 17, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494808, "dur": 17, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494828, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494846, "dur": 15, "ph": "X", "name": "ReadAsync 209", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494863, "dur": 15, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494880, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494899, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494916, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494935, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494952, "dur": 16, "ph": "X", "name": "ReadAsync 253", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494970, "dur": 20, "ph": "X", "name": "ReadAsync 231", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928494993, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495017, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495042, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495065, "dur": 16, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495083, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495101, "dur": 13, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495115, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495136, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495164, "dur": 28, "ph": "X", "name": "ReadAsync 33", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495195, "dur": 2, "ph": "X", "name": "ProcessMessages 715", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495198, "dur": 25, "ph": "X", "name": "ReadAsync 715", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495225, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495227, "dur": 26, "ph": "X", "name": "ReadAsync 708", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495257, "dur": 27, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495286, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495288, "dur": 22, "ph": "X", "name": "ReadAsync 590", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495312, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495314, "dur": 13, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495329, "dur": 14, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495345, "dur": 13, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495360, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495381, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495383, "dur": 27, "ph": "X", "name": "ReadAsync 202", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495412, "dur": 21, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495435, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495458, "dur": 19, "ph": "X", "name": "ReadAsync 567", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495479, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495501, "dur": 15, "ph": "X", "name": "ReadAsync 541", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495519, "dur": 18, "ph": "X", "name": "ReadAsync 74", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495539, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495556, "dur": 25, "ph": "X", "name": "ReadAsync 91", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495583, "dur": 16, "ph": "X", "name": "ReadAsync 214", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495601, "dur": 18, "ph": "X", "name": "ReadAsync 234", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495620, "dur": 15, "ph": "X", "name": "ReadAsync 192", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495637, "dur": 14, "ph": "X", "name": "ReadAsync 139", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495653, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495672, "dur": 15, "ph": "X", "name": "ReadAsync 222", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495689, "dur": 15, "ph": "X", "name": "ReadAsync 269", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495707, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495726, "dur": 13, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495741, "dur": 17, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495760, "dur": 15, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495778, "dur": 17, "ph": "X", "name": "ReadAsync 211", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495797, "dur": 15, "ph": "X", "name": "ReadAsync 264", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495814, "dur": 17, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495834, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495854, "dur": 15, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495871, "dur": 15, "ph": "X", "name": "ReadAsync 167", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495888, "dur": 16, "ph": "X", "name": "ReadAsync 126", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495905, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495924, "dur": 16, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495942, "dur": 19, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495963, "dur": 17, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928495982, "dur": 30, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496014, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496016, "dur": 95, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496113, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496116, "dur": 34, "ph": "X", "name": "ReadAsync 182", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496152, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496154, "dur": 31, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496189, "dur": 27, "ph": "X", "name": "ReadAsync 181", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496220, "dur": 31, "ph": "X", "name": "ReadAsync 197", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496254, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928496256, "dur": 28273, "ph": "X", "name": "ReadAsync 203", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524539, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524544, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524592, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524594, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524873, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524907, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524910, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524943, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524970, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928524972, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525002, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525004, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525040, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525042, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525076, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525078, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525101, "dur": 420, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525526, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525558, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525560, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525621, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525655, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525657, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525700, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525731, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525764, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525932, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928525934, "dur": 211, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526150, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526186, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526188, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526272, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526299, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526329, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526371, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526401, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526403, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526430, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526498, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526531, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526532, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526626, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526661, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526663, "dur": 280, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526947, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526978, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928526979, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527009, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527040, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527071, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527096, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527125, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527153, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527183, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527228, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527259, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527261, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527357, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527393, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527613, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527646, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527648, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527695, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527725, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527759, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527791, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527793, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527823, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527853, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527855, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527908, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928527939, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528021, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528053, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528267, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528300, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528345, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528372, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528374, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528432, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528462, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528464, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528505, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528507, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528537, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528563, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528592, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528624, "dur": 295, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528925, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528957, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928528959, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529074, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529109, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529111, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529143, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529145, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529200, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529232, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529234, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529265, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529266, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529297, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529331, "dur": 254, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529590, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529620, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529622, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529655, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529763, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529798, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529801, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529846, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529875, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529877, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529910, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529944, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529946, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928529977, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530038, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530072, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530326, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530362, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530391, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530431, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530466, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530500, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530529, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530531, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530558, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530559, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530593, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530625, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530627, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530658, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530723, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928530759, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531024, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531055, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531097, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531125, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531159, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531161, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531211, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531243, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531246, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531276, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531306, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531337, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531339, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531390, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531420, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531638, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531663, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531752, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531784, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531844, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531873, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531928, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531973, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928531975, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532002, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532079, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532114, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532116, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532165, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532193, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532246, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532282, "dur": 222, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532509, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532547, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532623, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532660, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532711, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532750, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532781, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532783, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532813, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532840, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532882, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532913, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532967, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928532998, "dur": 202, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533204, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533237, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533306, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533338, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533426, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533454, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533493, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533519, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533521, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533548, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533550, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533579, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533616, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533645, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533677, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533679, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533709, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533711, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533851, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533872, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533955, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533982, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928533983, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534043, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534070, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534072, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534100, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534129, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534180, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534211, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534238, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534390, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534419, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534421, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534515, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534546, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534548, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534614, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534644, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534712, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534743, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534745, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534797, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534826, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534927, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534957, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534959, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928534988, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535015, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535047, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535085, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535120, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535150, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535184, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535213, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535215, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535241, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535327, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535359, "dur": 320, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535684, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535713, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928535716, "dur": 476, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928536197, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928536230, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928536233, "dur": 4828, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928541067, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928541070, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928541107, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928541110, "dur": 2091, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928543207, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928543241, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928543243, "dur": 507, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928543756, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928543790, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928543792, "dur": 519, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928544317, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928544346, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928544349, "dur": 731, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928545085, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928545120, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928545123, "dur": 5979, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928551109, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928551112, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928551165, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928551169, "dur": 4073, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928555247, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928555250, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928555273, "dur": 1176, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928556455, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928556491, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928556495, "dur": 5485, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928561987, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928561989, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928562032, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928562036, "dur": 5344, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928567386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928567389, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928567435, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928567439, "dur": 9357, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928576805, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928576808, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928576837, "dur": 885, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928577727, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928577764, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928577848, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928577869, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928577960, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928577978, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928578071, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928578088, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928578182, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928578211, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928578390, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928578412, "dur": 338, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928578754, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928578775, "dur": 341, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928579120, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928579136, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928579454, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928579473, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928579773, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928579806, "dur": 243, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928580055, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928580074, "dur": 233, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928580311, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928580330, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928580452, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928580489, "dur": 273, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928580767, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928580833, "dur": 239, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581077, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581101, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581197, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581217, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581326, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581346, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581435, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581457, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581575, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581609, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581685, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581719, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581838, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928581869, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928582108, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928582140, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928582240, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928582266, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928582520, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928582535, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928582771, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928582790, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928583047, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928583062, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928583321, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928583339, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928583594, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928583613, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928583858, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928583873, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928584124, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928584149, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928584151, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928584427, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928584458, "dur": 223, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928584684, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928584700, "dur": 233, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928584939, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928584959, "dur": 244, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928585207, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928585241, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928585243, "dur": 257, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928585505, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928585530, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928585788, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928585808, "dur": 233, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928586044, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928586071, "dur": 223, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928586296, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928586315, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928586567, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928586593, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928586843, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928586863, "dur": 226, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587094, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587114, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587211, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587242, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587334, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587349, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587459, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587476, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587632, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928587650, "dur": 357, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588009, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588036, "dur": 339, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588381, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588398, "dur": 217, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588620, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588638, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588719, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588746, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588818, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588848, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588920, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928588946, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589077, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589107, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589235, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589266, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589366, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589397, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589557, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589579, "dur": 319, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589901, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928589921, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590012, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590031, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590128, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590144, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590294, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590310, "dur": 328, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590643, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590674, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590767, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590770, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590786, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590892, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928590908, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928591062, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928591081, "dur": 371, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928591456, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928591486, "dur": 351, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928591842, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928591873, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928591877, "dur": 335, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928592218, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928592238, "dur": 429, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928592671, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928592710, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928592712, "dur": 257, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928592973, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928593009, "dur": 332, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928593345, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928593362, "dur": 342, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928593708, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928593725, "dur": 311, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594041, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594062, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594162, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594189, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594192, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594284, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594312, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594315, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594412, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594439, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594441, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594557, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594592, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594724, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594752, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594754, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594865, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928594897, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595018, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595056, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595058, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595216, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595244, "dur": 322, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595571, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595609, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595612, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595702, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595733, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595756, "dur": 190, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928595950, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596077, "dur": 217, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596297, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596300, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596330, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596332, "dur": 185, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596522, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596551, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596593, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596625, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596627, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596739, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596771, "dur": 8, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596780, "dur": 65, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596850, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596876, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928596909, "dur": 843, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928597757, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928597759, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928597790, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928597792, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928597833, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928597859, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928597861, "dur": 1239, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599107, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599140, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599142, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599181, "dur": 28, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599210, "dur": 12, "ph": "X", "name": "ProcessMessages 22", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599224, "dur": 86, "ph": "X", "name": "ReadAsync 22", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599314, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599341, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599440, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599468, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599545, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599574, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599576, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599831, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599862, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599897, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599936, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928599968, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600255, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600285, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600463, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600495, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600497, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600529, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600560, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600611, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600640, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600672, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600674, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600702, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600759, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600796, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600922, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600954, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600956, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928600989, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601155, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601187, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601215, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601441, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601470, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601473, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601499, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601576, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601604, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601733, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601765, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601866, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601895, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928601897, "dur": 330, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602231, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602263, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602265, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602293, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602295, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602462, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602490, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602492, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602552, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602585, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602587, "dur": 378, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602968, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928602970, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603003, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603020, "dur": 78, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603103, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603133, "dur": 5, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603139, "dur": 200, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603345, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603374, "dur": 6, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603382, "dur": 359, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603746, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603783, "dur": 5, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603789, "dur": 39, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603833, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603894, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928603922, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928604003, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928604034, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928604036, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928604078, "dur": 1170, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605252, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605255, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605290, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605292, "dur": 270, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605567, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605604, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605754, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605788, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928605790, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606005, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606037, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606039, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606134, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606166, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606168, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606222, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606249, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606513, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606552, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606554, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606731, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928606766, "dur": 269, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607041, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607072, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607074, "dur": 78, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607157, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607185, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607238, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607243, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607271, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607345, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607374, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607431, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607465, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607482, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607515, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607578, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607609, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607611, "dur": 176, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607791, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607826, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607862, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607864, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607897, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607899, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607926, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928607956, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608022, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608043, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608090, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608119, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608210, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608243, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608263, "dur": 29, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608295, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608297, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608323, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608350, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608379, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608381, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608416, "dur": 4, "ph": "X", "name": "ProcessMessages 62", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608421, "dur": 32, "ph": "X", "name": "ReadAsync 62", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608456, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608458, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608495, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608523, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608574, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608603, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608605, "dur": 207, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608817, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608847, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608876, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608936, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608967, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928608970, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609046, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609078, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609205, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609230, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609278, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609310, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609312, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609344, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609495, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928609524, "dur": 874, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610401, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610403, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610439, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610443, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610471, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610492, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610554, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610585, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610587, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610619, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610664, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610692, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610694, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610739, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610771, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610814, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610842, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610870, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610872, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928610980, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611010, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611057, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611087, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611229, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611251, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611337, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611364, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611512, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611540, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611603, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611634, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611636, "dur": 164, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611805, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611843, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611968, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928611996, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612069, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612099, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612130, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612156, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612184, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612186, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612218, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612245, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612319, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612349, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612351, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612375, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612427, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612453, "dur": 231, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612688, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612715, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612718, "dur": 132, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612855, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612883, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928612885, "dur": 276, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613167, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613198, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613200, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613273, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613303, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613305, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613336, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613338, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613369, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613437, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613468, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613557, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613590, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613702, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613731, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613733, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613839, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613871, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928613970, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614001, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614051, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614053, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614080, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614082, "dur": 729, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614816, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614849, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614852, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614887, "dur": 30, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614920, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928614976, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615004, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615045, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615073, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615157, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615188, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615227, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615256, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615377, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615410, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615412, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615511, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615542, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615592, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615621, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615735, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615767, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615840, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615873, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615909, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615936, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928615939, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616170, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616196, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616238, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616271, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616304, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616339, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616372, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616402, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616432, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616463, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616631, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616658, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616706, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616734, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616794, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616820, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616822, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616857, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616915, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616944, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928616946, "dur": 374, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617325, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617349, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617351, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617390, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617392, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617410, "dur": 179, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617594, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617657, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617803, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928617834, "dur": 561, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928618400, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928618434, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928618436, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928618733, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928618735, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928618762, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928618807, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928618833, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928618835, "dur": 200, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619042, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619073, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619116, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619151, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619155, "dur": 133, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619293, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619324, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619354, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619356, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619387, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619418, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619421, "dur": 282, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619708, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619736, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619738, "dur": 67, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619809, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619843, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619849, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619894, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619920, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928619923, "dur": 87, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928620014, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928620029, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928620031, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928620082, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928620111, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928620113, "dur": 50, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928620166, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928620194, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928620196, "dur": 2462, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928622666, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928622669, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928622711, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928622726, "dur": 1313, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928624045, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928624047, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928624086, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928624100, "dur": 13241, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928637353, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928637358, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928637395, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928637412, "dur": 669, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638086, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638088, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638129, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638140, "dur": 63, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638208, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638237, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638242, "dur": 271, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638519, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638553, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638558, "dur": 53, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638616, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638646, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928638651, "dur": 20185, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928658848, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928658853, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928658896, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928658912, "dur": 68690, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928727613, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928727618, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928727655, "dur": 22, "ph": "X", "name": "ProcessMessages 444", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928727678, "dur": 47026, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928774714, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928774719, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928774771, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928774776, "dur": 1360, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928776140, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928776142, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928776187, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928776201, "dur": 9477, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928785688, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928785692, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928785729, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928785746, "dur": 32, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928785782, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928785784, "dur": 425, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928786214, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928786248, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754353928786250, "dur": 10993, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 55237263, "ts": 1754353928798275, "dur": 2409, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 68719476736, "ts": 1754353928446827, "dur": 350462, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754353928446926, "dur": 9978, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754353928797293, "dur": 11, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754353928797306, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 55237263, "ts": 1754353928800687, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754353928282019, "dur": 1736, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754353928283759, "dur": 161637, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754353928445398, "dur": 1409, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 93864, "tid": 55237263, "ts": 1754353928800697, "dur": 5, "ph": "X", "name": "", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754353928483796, "dur":2884, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353928486691, "dur":494, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353928487272, "dur":435, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353928497152, "dur":94, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":0, "ts":1754353928487834, "dur":9550, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353928497392, "dur":289531, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353928786924, "dur":243, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353928787353, "dur":3006, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754353928487851, "dur":9548, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928501458, "dur":308, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":1, "ts":1754353928501766, "dur":1472, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1754353928503238, "dur":15831, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1754353928519069, "dur":293, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":1, "ts":1754353928519362, "dur":4708, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":1, "ts":1754353928524070, "dur":283, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":1, "ts":1754353928524354, "dur":505, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1754353928524859, "dur":404, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":1, "ts":1754353928525263, "dur":94, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":1, "ts":1754353928525358, "dur":57, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1754353928497406, "dur":28024, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928525438, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754353928525509, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928525860, "dur":739, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928526605, "dur":162, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928526768, "dur":155, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928526930, "dur":212, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928527148, "dur":192, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928527353, "dur":221, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928527579, "dur":460, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928528041, "dur":677, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928528720, "dur":207, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928528933, "dur":918, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928529853, "dur":200, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928530055, "dur":204, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928530261, "dur":480, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928530743, "dur":266, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928531011, "dur":241, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928531258, "dur":195, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928531462, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928531688, "dur":216, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928531906, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928532096, "dur":427, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928532525, "dur":986, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928533513, "dur":221, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928533736, "dur":239, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928533977, "dur":174, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928534157, "dur":373, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928534532, "dur":214, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928534750, "dur":2509, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928537265, "dur":155, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928537422, "dur":159, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928537583, "dur":543, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928538127, "dur":298, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928538427, "dur":269, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928538698, "dur":702, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928539402, "dur":498, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928539902, "dur":264, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928540168, "dur":411, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928541265, "dur":185, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":1, "ts":1754353928541453, "dur":184, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754353928541639, "dur":172, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754353928541813, "dur":261, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":1, "ts":1754353928525643, "dur":16840, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754353928542484, "dur":13887, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928565946, "dur":11747, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1754353928577694, "dur":209, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928578718, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928578789, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928578872, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928578992, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928579110, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928579222, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928579365, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928579783, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928580156, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928580503, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928580803, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928581107, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928581370, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928581480, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928581801, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928582115, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928582224, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928582351, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928582484, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928582614, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928582722, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928582861, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928583172, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928583276, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928583578, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928583835, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928584106, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928584373, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928584657, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928584927, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928585180, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928585486, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928585754, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928586005, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928586275, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928586559, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928586849, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928587103, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928587368, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928587614, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928587905, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928588157, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928588237, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928588362, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928588490, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928588649, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928589032, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928589417, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928589688, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928589770, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928589876, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928589975, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928590077, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928590222, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928590378, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928590546, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928590933, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928591052, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928591171, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928591319, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928591669, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928591795, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928591922, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928592076, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928592464, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928592851, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928593234, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928593604, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928593979, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928594352, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928594733, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928595060, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928595188, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928595309, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928595430, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928595559, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928595703, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928595857, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928596014, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928596200, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928596593, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928596720, "dur":325, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928597045, "dur":186, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Transactions.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928597235, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928597446, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928597670, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928597883, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928598072, "dur":899, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928600299, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928600463, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928600585, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754353928600672, "dur":295, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928601458, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928601858, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928602461, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928602612, "dur":262, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928603322, "dur":280, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928603897, "dur":1255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928608027, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":1, "ts":1754353928608132, "dur":246, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928608396, "dur":338, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928608742, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928609239, "dur":401, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928609646, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder.Stl-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754353928609712, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928611385, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928611583, "dur":295, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928611888, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928612484, "dur":644, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928613137, "dur":320, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928613470, "dur":364, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928613878, "dur":533, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928615880, "dur":851, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928617785, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Domain_Reload-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754353928617874, "dur":611, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928619969, "dur":543, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754353928621231, "dur":38727, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":1, "ts":1754353928660078, "dur":126865, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353928487873, "dur":9534, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353928497738, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\WindowsBase.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928498472, "dur":852, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":2, "ts":1754353928499324, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":2, "ts":1754353928500011, "dur":726, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":2, "ts":1754353928500737, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928501434, "dur":676, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":2, "ts":1754353928502110, "dur":908, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928503018, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928503722, "dur":746, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":2, "ts":1754353928504468, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928505153, "dur":719, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928505873, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928506516, "dur":632, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928507148, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928507707, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928508386, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928509107, "dur":849, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928497417, "dur":12539, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353928509957, "dur":667, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":2, "ts":1754353928510624, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":2, "ts":1754353928511312, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.exe" }}
,{ "pid":12345, "tid":2, "ts":1754353928509957, "dur":5400, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353928515357, "dur":5852, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353928521209, "dur":2626, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353928523836, "dur":1596, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353928525440, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":2, "ts":1754353928525494, "dur":679, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353928526198, "dur":178, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754353928526389, "dur":181, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":2, "ts":1754353928526690, "dur":207, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928526898, "dur":229, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928527128, "dur":174, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928527304, "dur":190, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928527496, "dur":178, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928527676, "dur":387, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928528065, "dur":656, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928528723, "dur":247, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928528972, "dur":878, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928529852, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928530050, "dur":197, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928530256, "dur":493, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928530751, "dur":226, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928530979, "dur":280, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928531261, "dur":204, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928531467, "dur":215, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928531688, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928531874, "dur":192, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928532072, "dur":461, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928532535, "dur":961, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928533499, "dur":225, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928533730, "dur":235, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928533970, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928534165, "dur":379, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928534546, "dur":203, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928534751, "dur":2433, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928537186, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928537371, "dur":185, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928537558, "dur":557, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928538117, "dur":298, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928538423, "dur":266, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928538694, "dur":709, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928539405, "dur":505, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928539912, "dur":257, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928540171, "dur":426, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":2, "ts":1754353928526179, "dur":15121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754353928542066, "dur":114, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754353928543049, "dur":185682, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754353928775636, "dur":129, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754353928775554, "dur":212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754353928775812, "dur":1481, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1754353928777295, "dur":9634, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928487905, "dur":9513, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928497426, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928498027, "dur":882, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928498909, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928499684, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928500419, "dur":735, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928501154, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928501832, "dur":867, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928502700, "dur":776, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928503477, "dur":801, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928504278, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928504957, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928505632, "dur":767, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":3, "ts":1754353928506400, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928507028, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928507657, "dur":632, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928508289, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928508950, "dur":769, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928509719, "dur":841, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928510560, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928511225, "dur":745, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":3, "ts":1754353928497426, "dur":14544, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928512550, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":618, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Routing.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":670, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Discovery.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Security.dll" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":8806, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":4052, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":603, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":****************, "dur":633, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928526094, "dur":690, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928526790, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928527472, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928528138, "dur":697, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928528847, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928529514, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928530071, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928530737, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928531471, "dur":692, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928532171, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928532781, "dur":862, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928533652, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928534348, "dur":644, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928534998, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":3, "ts":1754353928535179, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928535372, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":3, "ts":1754353928535541, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928535762, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":3, "ts":1754353928535922, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928536081, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":3, "ts":1754353928536219, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928536343, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928568513, "dur":35612, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets" }}
,{ "pid":12345, "tid":3, "ts":1754353928604274, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928606779, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928608360, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928608726, "dur":436, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928609173, "dur":389, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928609611, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928611224, "dur":469, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928613023, "dur":553, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928614290, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928615954, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928616129, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928616305, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Burst.Unsafe-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754353928616366, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928618191, "dur":367, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928619471, "dur":736, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754353928621316, "dur":165608, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928487928, "dur":9497, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928497431, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928498088, "dur":841, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928498930, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928499651, "dur":743, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928500394, "dur":702, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928501096, "dur":712, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928501808, "dur":832, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928502640, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928503415, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928504196, "dur":735, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928504931, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928505615, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928506326, "dur":623, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928506949, "dur":635, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928507585, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928508215, "dur":714, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928508929, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928509679, "dur":873, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928510553, "dur":653, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928511206, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928497431, "dur":14483, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928511914, "dur":2718, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928514632, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.CJK.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928514632, "dur":5349, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928520926, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928519982, "dur":5102, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928525085, "dur":351, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928525451, "dur":750, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928526210, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928526889, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928527537, "dur":682, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928528225, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928528953, "dur":725, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928529685, "dur":708, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928530398, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928531023, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928531742, "dur":690, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928532437, "dur":782, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928533226, "dur":741, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928533973, "dur":729, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928534706, "dur":266, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928534973, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928535100, "dur":498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":4, "ts":1754353928535598, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928535873, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":4, "ts":1754353928536025, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928544895, "dur":59609, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":4, "ts":1754353928604654, "dur":489, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928606817, "dur":454, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928608087, "dur":209, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928608322, "dur":315, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928608645, "dur":383, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928609036, "dur":467, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928609552, "dur":404, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928611082, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928612212, "dur":439, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928612693, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928613278, "dur":96, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ImageConversionModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754353928614152, "dur":425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928615512, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928616664, "dur":313, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928616986, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928617595, "dur":359, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928617959, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Formats.Fbx.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754353928618013, "dur":719, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928618733, "dur":95, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Formats.Fbx.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754353928619851, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754353928621165, "dur":18060, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":4, "ts":1754353928639366, "dur":147580, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928487951, "dur":9484, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928497442, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928498160, "dur":842, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928499002, "dur":787, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928499789, "dur":726, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928500516, "dur":683, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928501200, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928501886, "dur":913, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928502799, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928503524, "dur":814, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928504338, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928505022, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928505682, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928506395, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928506993, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928507638, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928508242, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928508953, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928509745, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928510536, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928511160, "dur":714, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":5, "ts":1754353928497442, "dur":14433, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928511875, "dur":3539, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928515414, "dur":5163, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928520577, "dur":4595, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928525172, "dur":271, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928525463, "dur":775, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928526244, "dur":688, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928526937, "dur":629, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928527571, "dur":725, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928528301, "dur":598, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928528907, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928529618, "dur":753, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928530378, "dur":701, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928531085, "dur":647, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928531739, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928532470, "dur":836, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928533314, "dur":708, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928534030, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928534765, "dur":379, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":5, "ts":1754353928535145, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928535260, "dur":505, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":5, "ts":1754353928535765, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754353928537344, "dur":249455, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS" }}
,{ "pid":12345, "tid":6, "ts":1754353928487984, "dur":9460, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928497451, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928498122, "dur":855, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928498977, "dur":755, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928499732, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928500474, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928501210, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928501920, "dur":915, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928502836, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928503547, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928504342, "dur":707, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928505049, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928505673, "dur":703, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928506376, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928507003, "dur":619, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928507622, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928508234, "dur":722, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928508956, "dur":761, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928509717, "dur":824, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928510541, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928511184, "dur":759, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928497451, "dur":14492, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928512444, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\mscorlib.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928512958, "dur":683, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\monodoc.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928514095, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\WindowsBase.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928514640, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\WebMatrix.Data.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928515757, "dur":522, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.dll" }}
,{ "pid":12345, "tid":6, "ts":1754353928511944, "dur":8227, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928520172, "dur":5066, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928525238, "dur":208, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928525457, "dur":730, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928526196, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928526846, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928527519, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928528188, "dur":742, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928528937, "dur":686, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928529629, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928530255, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928530930, "dur":679, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928531615, "dur":740, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928532364, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928533076, "dur":806, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928533890, "dur":744, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928534643, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928535208, "dur":470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":6, "ts":1754353928535678, "dur":387, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928536072, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":6, "ts":1754353928536197, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928536336, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928563112, "dur":46270, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":6, "ts":1754353928609570, "dur":533, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928611509, "dur":228, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928611744, "dur":357, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928612110, "dur":629, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928612750, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterRendererModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754353928612804, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928614100, "dur":340, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928614483, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928616266, "dur":471, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928617816, "dur":1127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754353928620262, "dur":3535, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":6, "ts":1754353928624332, "dur":162605, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928488007, "dur":9444, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928497458, "dur":764, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928498222, "dur":814, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928499037, "dur":724, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928499761, "dur":716, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928500478, "dur":765, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928501243, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928501931, "dur":945, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928502876, "dur":653, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928503529, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928504307, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928504984, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928505733, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928506429, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928507020, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928507676, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928508384, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928509028, "dur":744, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928509772, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928510545, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928511201, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":7, "ts":1754353928497458, "dur":14432, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928511890, "dur":3171, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928515062, "dur":5610, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928520673, "dur":4369, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928525043, "dur":396, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928525457, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928526141, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928526781, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928526934, "dur":703, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928527646, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928528363, "dur":685, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928529056, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928529760, "dur":690, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928530456, "dur":716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928531180, "dur":679, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928531867, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928532538, "dur":846, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928533391, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928534109, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928534816, "dur":242, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":7, "ts":1754353928535058, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928535223, "dur":480, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":7, "ts":1754353928535704, "dur":451, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928544353, "dur":65194, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets" }}
,{ "pid":12345, "tid":7, "ts":1754353928609759, "dur":702, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928611554, "dur":248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928611812, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928612377, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Samples.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754353928612440, "dur":865, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928614124, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928614339, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928616233, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928616378, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928616529, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928616658, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928617060, "dur":438, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928617513, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928617977, "dur":502, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928619881, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754353928621038, "dur":18322, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0" }}
,{ "pid":12345, "tid":7, "ts":1754353928639672, "dur":147260, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928488031, "dur":9427, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928497464, "dur":798, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928498262, "dur":898, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928499161, "dur":746, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928499907, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928500601, "dur":715, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928501316, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928502022, "dur":921, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928502943, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928503647, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928504387, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928505108, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928505816, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928506512, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928507123, "dur":614, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928507737, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928508384, "dur":670, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928509054, "dur":968, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928510022, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928510655, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928511294, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928497463, "dur":14572, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928512984, "dur":580, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Interfaces.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928513564, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Experimental.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928514131, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Debugger.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928514682, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Core.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928515671, "dur":605, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928517489, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.Formatting.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928512036, "dur":8935, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928520971, "dur":3914, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928524885, "dur":552, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928525451, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928526019, "dur":667, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928526700, "dur":613, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928527319, "dur":441, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928527768, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928528504, "dur":658, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928529170, "dur":556, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928529731, "dur":686, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928530425, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928531112, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928531780, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928532451, "dur":787, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928533250, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928533982, "dur":705, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928534694, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928534825, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":8, "ts":1754353928535034, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928535232, "dur":506, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":8, "ts":1754353928535739, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928537279, "dur":59616, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets2.assets" }}
,{ "pid":12345, "tid":8, "ts":1754353928597292, "dur":438, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928597741, "dur":246, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928598026, "dur":941, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928599709, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928600852, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928601541, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928601679, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928601816, "dur":247, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928602105, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928602545, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928603152, "dur":254, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928603829, "dur":655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928605981, "dur":908, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928607722, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928607874, "dur":249, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928608123, "dur":69, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":8, "ts":1754353928608197, "dur":374, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928608581, "dur":433, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928609028, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928609494, "dur":247, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928609777, "dur":655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928611423, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928611659, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928613152, "dur":321, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928613490, "dur":340, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928613842, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928614039, "dur":411, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928615447, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928617153, "dur":375, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928617535, "dur":315, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928617857, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754353928617942, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928619385, "dur":563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754353928621417, "dur":165503, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928488053, "dur":9433, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928497494, "dur":765, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928498259, "dur":877, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928499137, "dur":653, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928499790, "dur":744, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928500534, "dur":707, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928501241, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928501945, "dur":956, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928502902, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928503608, "dur":752, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928504361, "dur":716, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928505077, "dur":724, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928505802, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928506463, "dur":614, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928507077, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928507706, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928508386, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928509081, "dur":794, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928509875, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928510633, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928511300, "dur":743, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928497493, "dur":14550, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928512448, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\RabbitMQ.Client.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928513014, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\PEAPI.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928513604, "dur":557, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Novell.Directory.Ldap.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928514161, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.XBuild.Tasks.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928514800, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.WebBrowser.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928515452, "dur":568, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Tasklets.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928517185, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Profiler.Log.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928518538, "dur":501, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Options.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928512043, "dur":9188, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928521231, "dur":235, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928521466, "dur":3968, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928526074, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928526766, "dur":644, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928527422, "dur":903, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928528339, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928528989, "dur":658, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928529652, "dur":686, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928530346, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928530995, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928531660, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928532380, "dur":708, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928533094, "dur":868, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928533968, "dur":694, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928534668, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928535190, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":9, "ts":1754353928535370, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928535536, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":9, "ts":1754353928535718, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928535859, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":9, "ts":1754353928536047, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928545461, "dur":58844, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":9, "ts":1754353928604495, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928607257, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928608532, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928608975, "dur":525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928609553, "dur":463, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928611116, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928612366, "dur":742, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928613118, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928613352, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928613500, "dur":329, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928613838, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928614036, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928615505, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754353928615575, "dur":542, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928616967, "dur":481, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928617458, "dur":313, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928617780, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928618096, "dur":440, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928619377, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754353928620856, "dur":18813, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1" }}
,{ "pid":12345, "tid":9, "ts":1754353928639801, "dur":147133, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928488082, "dur":9412, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928497501, "dur":744, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928498245, "dur":846, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928499091, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928499830, "dur":728, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928500558, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928501298, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928502002, "dur":928, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928502930, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928503666, "dur":769, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928504435, "dur":726, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928505161, "dur":683, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928505844, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928506524, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928507171, "dur":606, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928507777, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928508425, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928509130, "dur":923, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928510053, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928510681, "dur":681, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928511362, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928497500, "dur":14540, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928514569, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Design.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928515678, "dur":532, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928517469, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Linq.dll" }}
,{ "pid":12345, "tid":10, "ts":1754353928512041, "dur":8602, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928520643, "dur":3488, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928524131, "dur":1337, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928525469, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928526172, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928526882, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928527523, "dur":733, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928528262, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928528931, "dur":682, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928529618, "dur":593, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928530218, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928530910, "dur":661, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928531578, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928532289, "dur":697, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928532994, "dur":853, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928533855, "dur":709, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928534572, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928535104, "dur":420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":10, "ts":1754353928535525, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928535665, "dur":367, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":10, "ts":1754353928536033, "dur":322, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928557594, "dur":42749, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":10, "ts":1754353928600534, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928601103, "dur":289, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928601697, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928601834, "dur":235, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928602108, "dur":228, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928602622, "dur":383, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928603517, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928603714, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UmbraModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754353928603788, "dur":477, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928605552, "dur":827, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928607631, "dur":504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928608135, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.State-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754353928608692, "dur":378, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928609079, "dur":445, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928609560, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928611155, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928612682, "dur":525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928614039, "dur":463, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928615494, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928616844, "dur":492, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928617369, "dur":478, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928619130, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754353928620553, "dur":4635, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1.resS" }}
,{ "pid":12345, "tid":10, "ts":1754353928625304, "dur":161643, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928488106, "dur":9394, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928497506, "dur":810, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928498317, "dur":895, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928499212, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928499922, "dur":730, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928500653, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928501378, "dur":683, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928502061, "dur":966, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928503027, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928503752, "dur":771, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928504523, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928505228, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928505921, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928506509, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928507098, "dur":580, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928507678, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928508379, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928509047, "dur":740, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928509787, "dur":816, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928510604, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928511269, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928497506, "dur":14458, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928514788, "dur":607, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.RegularExpressions.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928515784, "dur":548, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Mvc.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928517291, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Http.WebHost.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928511964, "dur":8675, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928521428, "dur":531, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\Microsoft.Win32.Registry.AccessControl.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928520639, "dur":3933, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928524572, "dur":872, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928525454, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928526143, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928526775, "dur":668, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928527455, "dur":650, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928528111, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928528761, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928529408, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928530095, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928530793, "dur":719, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928531518, "dur":716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928532244, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928532900, "dur":862, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928533770, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928534450, "dur":672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928535126, "dur":625, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":11, "ts":1754353928535752, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928535945, "dur":210, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1754353928536156, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928552225, "dur":84, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928595926, "dur":1974, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":11, "ts":1754353928598060, "dur":827, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928599657, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928600801, "dur":208, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928601467, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928601613, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928601760, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928601937, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928602496, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928603117, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928604523, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClothModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754353928604574, "dur":563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928606553, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928608044, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928608218, "dur":267, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928608495, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928608948, "dur":457, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928609412, "dur":305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928609758, "dur":650, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928611444, "dur":258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928611716, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928612001, "dur":737, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928612778, "dur":495, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928614167, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928615616, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928616854, "dur":454, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928617317, "dur":245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928617570, "dur":380, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928617982, "dur":477, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928619492, "dur":482, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928638431, "dur":68, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754353928620957, "dur":17557, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0.resS" }}
,{ "pid":12345, "tid":11, "ts":1754353928638682, "dur":148239, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928488136, "dur":9372, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928497509, "dur":735, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928498245, "dur":915, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928499161, "dur":752, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928499913, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928500669, "dur":760, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928501429, "dur":693, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_7.0.1323.51816.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928502122, "dur":958, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928503080, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928503769, "dur":769, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928504538, "dur":712, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928505250, "dur":765, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928506015, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928506604, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928507233, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928507831, "dur":615, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928508446, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928509154, "dur":938, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928510093, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928510704, "dur":676, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928511380, "dur":722, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":12, "ts":1754353928497509, "dur":14594, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928513320, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.C5.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928513998, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Btls.Interface.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928514594, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Web.Infrastructure.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928515749, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928516805, "dur":505, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Utilities.v4.0.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928517310, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Tasks.v4.0.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928517905, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Framework.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928512103, "dur":9045, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928521149, "dur":4066, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928525215, "dur":233, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":****************, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928526025, "dur":637, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928526677, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928527299, "dur":787, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928528093, "dur":744, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928528847, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928529493, "dur":780, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928530278, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928530933, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928531681, "dur":705, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928532396, "dur":735, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928533136, "dur":803, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928533944, "dur":852, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928534801, "dur":230, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":12, "ts":1754353928535032, "dur":288, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928535328, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":12, "ts":1754353928535490, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928535662, "dur":302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":12, "ts":1754353928535965, "dur":297, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928546229, "dur":58667, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":12, "ts":1754353928605112, "dur":1594, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928608503, "dur":436, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928608949, "dur":481, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928609450, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Xml.dll" }}
,{ "pid":12345, "tid":12, "ts":1754353928609508, "dur":236, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928609783, "dur":850, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928611633, "dur":321, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928611963, "dur":982, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928612952, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VirtualTexturingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754353928613033, "dur":535, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928614396, "dur":796, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928616800, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928617411, "dur":573, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928619428, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754353928619508, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754353928621315, "dur":18456, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":12, "ts":1754353928639943, "dur":146992, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754353928795649, "dur":2148, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 55237263, "ts": 1754353928800732, "dur": 2581, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 55237263, "ts": 1754353928803445, "dur": 2835, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 55237263, "ts": 1754353928798271, "dur": 8044, "ph": "X", "name": "Write chrome-trace events", "args": {} },
