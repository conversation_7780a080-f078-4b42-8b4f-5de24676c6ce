using UnityEngine;

[CreateAssetMenu(fileName = "New Consumable", menuName = "Items/Consumable")]
public partial class ConsumableDefinition : Item
{
    public int EnergyRestore;
    public int HealthRestore;
    
    public virtual void Use(PlayerStatus playerStatus)
    {
        if (playerStatus != null)
        {
            if (EnergyRestore > 0)
            {
                playerStatus.UpdateEnergy(playerStatus.currentEnergy + EnergyRestore);
            }
            if (HealthRestore > 0)
            {
                playerStatus.RestoreHealth(HealthRestore);
            }
        }
    }
}