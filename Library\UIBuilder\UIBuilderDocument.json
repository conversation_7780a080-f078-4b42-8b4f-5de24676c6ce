{"MonoBehaviour": {"m_Enabled": true, "m_EditorHideFlags": 0, "m_Name": "BuilderDocument", "m_EditorClassIdentifier": "UnityEditor.UIBuilderModule:Unity.UI.Builder:BuilderDocument", "m_SavedBuilderUxmlToThemeStyleSheetList": [], "m_CurrentCanvasTheme": 4, "m_CurrentCanvasThemeStyleSheetReference": {"fileID": -4733365628477956816, "guid": "13ebcac39ef228644bd4414cc66bc574", "type": 3}, "m_CodePreviewVisible": true, "m_OpenUXMLFiles": [{"m_OpenUSSFiles": [{"m_StyleSheet": {"fileID": 7433441132597879392, "guid": "42f6485d91c876f4b926c7584655603d", "type": 3}, "m_ContentHash": -751057782, "m_UssPreview": ".root-container {\r\n    flex-grow: 1;\r\n    flex-direction: column;\r\n    justify-content: flex-end;\r\n    align-items: center;\r\n    -unity-font-definition: resource('SpaceGrotesk-Medium');\r\n}\r\n\r\n.main-container {\r\n    flex-grow: 0;\r\n    flex-shrink: 0;\r\n    flex-direction: row;\r\n    width: 100%;\r\n    justify-content: center;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.panel {\r\n    background-color: rgb(0, 0, 0);\r\n    border-width: 5px;\r\n    border-color: rgb(14, 14, 14);\r\n    flex-grow: 0;\r\n}\r\n\r\n.panel--secondary {\r\n    border-left-width: 0;\r\n}\r\n\r\n#StashContainer {\r\n    border-bottom-width: 0;\r\n    width: 100%;\r\n}\r\n\r\n#StashMainContainer {\r\n    width: 446px;\r\n}\r\n\r\n.header {\r\n    width: 100%;\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n    padding: 16px 24px 16px 24px;\r\n    background-color: rgb(0, 0, 0);\r\n    align-items: center;\r\n    border-bottom-width: 5px;\r\n    border-bottom-color: rgb(14, 14, 14);\r\n}\r\n\r\n.header__icon {\r\n    width: 16px;\r\n    height: 16px;\r\n}\r\n\r\n.header__title {\r\n    color: rgba(197, 197, 197, 0.25);\r\n    font-size: 16px;\r\n    -unity-font-definition: resource('SpaceGrotesk-Bold');\r\n}\r\n\r\n.header__money {\r\n    color: rgba(197, 197, 197, 0.25);\r\n    font-size: 16px;\r\n    -unity-font-definition: resource('SpaceGrotesk-Bold');\r\n}\r\n\r\n.digit-zero {\r\n    color: rgb(70, 70, 70);\r\n}\r\n\r\n.digit-value {\r\n    color: rgb(150, 150, 150);\r\n}\r\n\r\n.currency-symbol {\r\n    color: rgb(70, 70, 70);\r\n}\r\n\r\n.equipment-area {\r\n    display: flex;\r\n    flex-direction: row;\r\n    justify-content: center;\r\n    padding: 24px 24px 0 24px;\r\n    background-color: rgb(10, 10, 10);\r\n    min-height: 136px;\r\n    align-items: center;\r\n}\r\n\r\n#EquipmentArea {\r\n    background-color: rgb(0, 0, 0);\r\n}\r\n\r\n.equipment-slots {\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n}\r\n\r\n#HeadSlot {\r\n    width: 112px;\r\n    height: 112px;\r\n    min-width: 112px;\r\n    min-height: 112px;\r\n    max-width: 112px;\r\n    max-height: 112px;\r\n    margin-right: 0;\r\n    flex-shrink: 0;\r\n    flex-grow: 0;\r\n    transition-property: background-color, border-color;\r\n    transition-duration: 0.1s;\r\n    transition-timing-function: ease;\r\n}\r\n\r\n#ChestSlot {\r\n    width: 112px;\r\n    height: 112px;\r\n    min-width: 112px;\r\n    min-height: 112px;\r\n    max-width: 112px;\r\n    max-height: 112px;\r\n    margin-right: 0;\r\n    flex-shrink: 0;\r\n    flex-grow: 0;\r\n    transition-property: background-color, border-color;\r\n    transition-duration: 0.1s;\r\n    transition-timing-function: ease;\r\n}\r\n\r\n#BagSlot {\r\n    width: 112px;\r\n    height: 112px;\r\n    min-width: 112px;\r\n    min-height: 112px;\r\n    max-width: 112px;\r\n    max-height: 112px;\r\n    margin-right: 0;\r\n    flex-shrink: 0;\r\n    flex-grow: 0;\r\n    transition-property: background-color, border-color;\r\n    transition-duration: 0.1s;\r\n    transition-timing-function: ease;\r\n}\r\n\r\n.equipment-slot {\r\n    width: 112px;\r\n    height: 112px;\r\n    min-width: 112px;\r\n    min-height: 112px;\r\n    max-width: 112px;\r\n    max-height: 112px;\r\n    margin-right: 0;\r\n    flex-shrink: 0;\r\n    flex-grow: 0;\r\n    transition-property: background-color, border-color;\r\n    transition-duration: 0.1s;\r\n    transition-timing-function: ease;\r\n}\r\n\r\n.equipment-slot.empty {\r\n    border-width: 0;\r\n    background-color: rgb(20, 20, 20);\r\n}\r\n\r\n.equipment-slot.filled {\r\n    border-width: 1px;\r\n    border-color: rgb(60, 60, 60);\r\n    background-color: rgb(42, 42, 42);\r\n}\r\n\r\n.equipment-slot.empty .equipment-slot-img {\r\n    width: 70%;\r\n    height: 70%;\r\n    align-self: center;\r\n    -unity-background-scale-mode: scale-to-fit;\r\n}\r\n\r\n.equipment-slot.filled .equipment-slot-img {\r\n    width: 70%;\r\n    height: 70%;\r\n    background-image: none;\r\n}\r\n\r\n.equipment-slot.filled:hover {\r\n    background-color: rgb(50, 50, 50);\r\n    border-color: rgb(80, 80, 80);\r\n}\r\n\r\n.equipment-slot.empty:hover {\r\n    background-color: rgb(30, 30, 30);\r\n}\r\n\r\n.equipment-slot.valid-target {\r\n    background-color: rgba(0, 150, 60, 0.3);\r\n    border-width: 1px;\r\n    border-color: rgb(0, 150, 60);\r\n}\r\n\r\n.equipment-slot-img {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.scroll-view {\r\n    flex-grow: 1;\r\n    padding: 0;\r\n    overflow: hidden;\r\n}\r\n\r\n#StashScrollView {\r\n    width: auto;\r\n    background-color: rgb(0, 0, 0);\r\n    padding: 24px;\r\n    overflow: hidden;\r\n}\r\n\r\n#Inv {\r\n    align-self: center;\r\n    background-color: rgb(0, 0, 0);\r\n    width: 100%;\r\n    padding: 24px;\r\n}\r\n\r\n.inventory-placeholder {\r\n    height: 210px;\r\n    width: 100%;\r\n    background-color: rgb(0, 0, 0);\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 16px;\r\n    border-width: 1px;\r\n    border-color: rgb(26, 26, 26);\r\n    border-style: dotted;\r\n}\r\n\r\n.inventory-placeholder-text {\r\n    color: rgb(100, 100, 100);\r\n    -unity-text-align: middle-center;\r\n    font-size: 14px;\r\n    padding: 16px;\r\n}\r\n\r\n.grid-container {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n    flex-direction: row;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background-color: rgb(0, 0, 0);\r\n}\r\n\r\n#StashGrid {\r\n    display: grid;\r\n    grid-template-columns: ;\r\n    grid-template-rows: ;\r\n    grid-gap: 1px;\r\n    align-self: center;\r\n    justify-self: center;\r\n    background-color: rgb(0, 0, 0);\r\n}\r\n\r\n#InvGrid {\r\n    display: grid;\r\n    grid-template-columns: ;\r\n    grid-template-rows: ;\r\n    grid-gap: 1px;\r\n    align-self: center;\r\n    justify-self: center;\r\n    background-color: rgb(0, 0, 0);\r\n}\r\n\r\n#ShopGrid {\r\n    display: flex;\r\n    flex-direction: row;\r\n    flex-wrap: wrap;\r\n    width: 100%;\r\n    grid-template-columns: ;\r\n    grid-template-rows: ;\r\n    grid-gap: 2px;\r\n    align-self: center;\r\n    justify-self: center;\r\n    padding: 24px;\r\n    background-color: rgb(0, 0, 0);\r\n}\r\n\r\n#SellGrid {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n    align-self: center;\r\n    justify-self: center;\r\n    background-color: rgb(0, 0, 0);\r\n    padding: 24px;\r\n}\r\n\r\n#ShopScrollView {\r\n    overflow: hidden;\r\n}\r\n\r\n.inventory-item {\r\n    border-width: 0;\r\n    background-color: rgb(0, 0, 0);\r\n    width: 48px;\r\n    height: 48px;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.inventory-slot {\r\n    border: 1px solid rgb(0, 0, 0);\r\n}\r\n\r\n.shop-item {\r\n    border-width: 1px;\r\n    border-color: rgb(60, 60, 60);\r\n    background-color: rgb(25, 25, 25);\r\n    width: 60px;\r\n    height: 60px;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: absolute;\r\n}\r\n\r\n.shop-item--selected {\r\n    border-color: rgb(0, 150, 60);\r\n    background-color: rgb(35, 35, 35);\r\n}\r\n\r\n.shop-item-icon {\r\n    width: 80%;\r\n    height: 80%;\r\n    align-self: center;\r\n}\r\n\r\n.shop-item-price {\r\n    position: absolute;\r\n    bottom: 2px;\r\n    right: 2px;\r\n    color: rgb(255, 255, 255);\r\n    font-size: 10px;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n    padding: 1px 3px;\r\n    border-radius: 2px;\r\n}\r\n\r\n.price-cannot-afford {\r\n    color: rgb(153, 153, 153);\r\n}\r\n\r\n.ItemSlot {\r\n    background-color: rgb(25, 25, 25);\r\n    border-width: 1px;\r\n    border-color: rgb(0, 0, 0);\r\n    width: 64px;\r\n    height: 64px;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.ItemSlotWithItem {\r\n    background-color: rgb(30, 30, 30);\r\n    border-width: 1px;\r\n    border-color: rgb(0, 0, 0);\r\n    width: 64px;\r\n    height: 64px;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    transition-property: all;\r\n    transition-duration: 0.15s;\r\n    transition-timing-function: ease-out;\r\n    will-change: background-color, border-color;\r\n}\r\n\r\n.ItemSlotWithItem:hover {\r\n    background-color: rgb(40, 40, 40);\r\n    border-color: rgb(100, 100, 100);\r\n    transition-property: background-color, border-color;\r\n    transition-duration: 0s;\r\n    will-change: background-color, border-color;\r\n}\r\n\r\n.ItemSlotWithItem:active {\r\n    background-color: rgb(50, 50, 50);\r\n    border-color: rgb(120, 120, 120);\r\n}\r\n\r\n.slot-hovered {\r\n    background-color: rgb(40, 40, 40);\r\n    border-color: rgb(100, 100, 100);\r\n}\r\n\r\n.BuySlot {\r\n    background-color: rgb(25, 25, 25);\r\n    border-width: 1px;\r\n    border-color: rgb(40, 40, 40);\r\n    width: 64px;\r\n    height: 64px;\r\n}\r\n\r\n.BuySlotWithItem {\r\n    background-color: rgb(25, 25, 25);\r\n    border-width: 1px;\r\n    border-color: rgb(0, 120, 50);\r\n    width: 64px;\r\n    height: 64px;\r\n}\r\n\r\n.SellSlot {\r\n    background-color: rgb(30, 30, 30);\r\n    border-width: 1px;\r\n    border-color: rgb(60, 60, 60);\r\n    margin: 0;\r\n    padding: 0;\r\n    width: 64px;\r\n    height: 64px;\r\n}\r\n\r\n.SellSlot:hover {\r\n    background-color: rgb(40, 40, 40);\r\n    border-color: rgb(80, 80, 80);\r\n}\r\n\r\n.SellSlotWithItem {\r\n    background-color: rgb(35, 35, 35);\r\n    border-width: 1px;\r\n    border-color: rgb(100, 100, 100);\r\n}\r\n\r\n.SellSlotWithItem:hover {\r\n    background-color: rgb(45, 45, 45);\r\n    border-color: rgb(120, 120, 120);\r\n}\r\n\r\n.drag-hover-valid {\r\n    background-color: rgba(0, 150, 60, 0.3);\r\n    border-color: rgb(0, 150, 60);\r\n}\r\n\r\n.stats {\r\n    padding: 8px 16px;\r\n    flex-direction: row;\r\n    background-color: rgb(10, 10, 10);\r\n}\r\n\r\n.stats__label {\r\n    -unity-font-definition: resource('SpaceGrotesk-Bold');\r\n    margin-right: 24px;\r\n}\r\n\r\n.stats__weight {\r\n    color: rgb(244, 162, 97);\r\n}\r\n\r\n.stats__energy {\r\n    color: rgb(97, 182, 244);\r\n}\r\n\r\n.energy-container {\r\n    padding: 8px 24px 8px 24px;\r\n    background-color: rgb(0, 0, 0);\r\n    height: 40px;\r\n    justify-content: center;\r\n    border-top-width: 5px;\r\n    border-color: rgb(14, 14, 14);\r\n}\r\n\r\n.battery-indicator {\r\n    height: 20px;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    background-color: transparent;\r\n}\r\n\r\n.battery-icon {\r\n    width: 24px;\r\n    height: 24px;\r\n    -unity-background-scale-mode: scale-to-fit;\r\n    background-color: transparent;\r\n}\r\n\r\n.battery-bar {\r\n    flex-grow: 1;\r\n    height: 4px;\r\n    margin-left: 16px;\r\n    background-color: transparent;\r\n    flex-direction: row;\r\n    position: relative;\r\n}\r\n\r\n.battery-fill {\r\n    height: 100%;\r\n    position: absolute;\r\n    display: flex;\r\n}\r\n\r\n.battery-fill--low {\r\n    background-color: rgb(55, 55, 55);\r\n    width: 15%;\r\n    left: 0;\r\n}\r\n\r\n.battery-fill--medium {\r\n    background-color: rgb(94, 94, 94);\r\n    width: 35%;\r\n    left: 15%;\r\n}\r\n\r\n.battery-fill--high {\r\n    background-color: rgb(169, 169, 169);\r\n    width: 50%;\r\n    left: 50%;\r\n}\r\n\r\n.shop-controls {\r\n    flex-direction: row;\r\n    align-items: right;\r\n    justify-content: right;\r\n    padding: 24px 24px 0 24px;\r\n    border-bottom-width: 0;\r\n}\r\n\r\n.shop-footer {\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background-color: rgb(0, 0, 0);\r\n    padding: 16px;\r\n    border-top-width: 5px;\r\n    border-color: rgb(14, 14, 14);\r\n}\r\n\r\n.value-info {\r\n    flex-direction: row;\r\n    align-items: center;\r\n}\r\n\r\n.pixel-button {\r\n    width: 60px;\r\n    height: 30px;\r\n    background-color: rgba(33, 33, 33, 0.31);\r\n    color: rgb(159, 159, 159);\r\n    border-width: 0;\r\n    margin: 0;\r\n    -unity-font-definition: resource('SpaceGrotesk-Medium');\r\n}\r\n\r\n.pixel-button:hover {\r\n    background-color: rgba(33, 33, 33, 0.5);\r\n    color: white;\r\n}\r\n\r\n.pixel-button:active {\r\n    background-color: rgba(33, 33, 33, 0.7);\r\n}\r\n\r\n.pixel-button:disabled {\r\n    background-color: rgba(255, 255, 255, 0.1);\r\n    color: rgba(255, 255, 255, 0.4);\r\n}\r\n\r\n.pixel-button--full-width {\r\n    width: 100%;\r\n    margin-top: 16px;\r\n}\r\n\r\n.pixel-button--mode {\r\n    width: 80px;\r\n    height: 36px;\r\n}\r\n\r\n.pixel-button--active {\r\n    background-color: rgba(66, 135, 255, 0.25);\r\n    color: rgb(66, 135, 255);\r\n}\r\n\r\n#BuyModeButton.pixel-button--active {\r\n    background-color: rgba(66, 135, 255, 0.25);\r\n    color: rgb(66, 135, 255);\r\n}\r\n\r\n#SellModeButton {\r\n    margin-left: 8px;\r\n}\r\n\r\n#SellModeButton.pixel-button--active {\r\n    background-color: rgba(255, 132, 66, 0.25);\r\n    color: rgb(255, 132, 66);\r\n}\r\n\r\n.pixel-button--action {\r\n    background-color: rgba(66, 135, 255, 0.25);\r\n    color: rgb(66, 135, 255);\r\n    height: 36px;\r\n}\r\n\r\n.pixel-button--action:hover {\r\n    background-color: rgba(66, 135, 255, 0.4);\r\n    color: rgb(66, 135, 255);\r\n}\r\n\r\n.pixel-button--buy {\r\n    background-color: rgba(66, 135, 255, 0.25);\r\n    color: rgb(66, 135, 255);\r\n    height: 36px;\r\n}\r\n\r\n.pixel-button--buy:hover {\r\n    background-color: rgba(66, 135, 255, 0.4);\r\n    color: rgb(66, 135, 255);\r\n}\r\n\r\n.pixel-button--buy:active {\r\n    background-color: rgba(66, 135, 255, 0.6);\r\n    color: rgb(66, 135, 255);\r\n}\r\n\r\n.pixel-button--sell {\r\n    background-color: rgba(255, 132, 66, 0.25);\r\n    color: rgb(255, 132, 66);\r\n    height: 36px;\r\n}\r\n\r\n.pixel-button--sell:hover {\r\n    background-color: rgba(255, 132, 66, 0.4);\r\n    color: rgb(255, 132, 66);\r\n}\r\n\r\n.pixel-button--sell:active {\r\n    background-color: rgba(255, 132, 66, 0.6);\r\n    color: rgb(255, 132, 66);\r\n}\r\n\r\n.mode-container {\r\n    width: 100%;\r\n    flex-grow: 1;\r\n    flex-shrink: 0;\r\n    flex-direction: column;\r\n    min-height: 0;\r\n}\r\n\r\n.grid-section {\r\n    padding: 8px;\r\n    background-color: rgb(15, 15, 15);\r\n}\r\n\r\n.trade-details {\r\n    padding: 8px;\r\n    background-color: rgb(24, 24, 24);\r\n    flex-direction: row;\r\n    margin-top: 8px;\r\n}\r\n\r\n.trade-info {\r\n    background-color: rgb(26, 26, 26);\r\n    flex-grow: 1;\r\n    justify-content: space-between;\r\n    padding: 16px;\r\n}\r\n\r\n.label-text {\r\n    color: rgb(255, 0, 0);\r\n}\r\n\r\n.label-value {\r\n    color: rgb(255, 255, 255);\r\n}\r\n\r\n.instructions {\r\n    background-color: rgb(30, 30, 30);\r\n    -unity-text-align: right;\r\n}\r\n\r\n.instructions-container {\r\n    width: 100%;\r\n    padding: 0 16px;\r\n    border-top-width: 5px;\r\n    border-top-color: rgb(14, 14, 14);\r\n}\r\n\r\n.instructions-wrapper {\r\n    width: 100%;\r\n}\r\n\r\n.instruction-text {\r\n    color: rgb(51, 51, 51);\r\n    -unity-text-align: right;\r\n    -unity-font-definition: resource('SpaceGrotesk-Medium');\r\n}\r\n\r\n.no-items-message {\r\n    color: rgb(128, 128, 128);\r\n    -unity-text-align: middle-center;\r\n    width: 100%;\r\n    height: 100px;\r\n}\r\n\r\n#Tooltip {\r\n    position: absolute;\r\n    background-color: rgb(0, 0, 0);\r\n    border-width: 5px;\r\n    border-color: rgb(14, 14, 14);\r\n    padding-top: 16px;\r\n    padding-bottom: 16px;\r\n    padding-left: 24px;\r\n    padding-right: 24px;\r\n    min-width: 250px;\r\n    max-width: 350px;\r\n    display: none;\r\n    flex-direction: column;\r\n}\r\n\r\n#Tooltip TextField {\r\n    margin-bottom: 10px;\r\n    font-size: 16px;\r\n    -unity-font-style: bold;\r\n    background-color: rgb(25, 25, 25);\r\n    color: rgb(255, 255, 255);\r\n    border-width: 1px;\r\n    border-color: rgb(60, 60, 60);\r\n    padding-top: 6px;\r\n    padding-bottom: 6px;\r\n    padding-left: 8px;\r\n    padding-right: 8px;\r\n}\r\n\r\n#Tooltip Label {\r\n    font-size: 13px;\r\n    color: rgb(197, 197, 197);\r\n    white-space: normal;\r\n    margin-top: 4px;\r\n}\r\n\r\n.tooltip-container {\r\n    position: absolute;\r\n    background-color: rgb(0, 0, 0);\r\n    border-top-width: 5px;\r\n    border-bottom-width: 5px;\r\n    border-left-width: 5px;\r\n    border-right-width: 5px;\r\n    border-color: rgb(14, 14, 14);\r\n    padding-top: 16px;\r\n    padding-bottom: 16px;\r\n    padding-left: 24px;\r\n    padding-right: 24px;\r\n    min-width: 250px;\r\n    max-width: 350px;\r\n    flex-direction: column;\r\n}\r\n\r\n.tooltip-name-field {\r\n    margin-bottom: 10px;\r\n    font-size: 16px;\r\n    -unity-font-style-and-weight: bold;\r\n    background-color: rgb(25, 25, 25);\r\n    color: rgb(255, 255, 255);\r\n    border-top-width: 1px;\r\n    border-bottom-width: 1px;\r\n    border-left-width: 1px;\r\n    border-right-width: 1px;\r\n    border-color: rgb(60, 60, 60);\r\n    padding-top: 6px;\r\n    padding-bottom: 6px;\r\n    padding-left: 8px;\r\n    padding-right: 8px;\r\n}\r\n\r\n.tooltip-name-field .unity-text-field__input {\r\n    background-color: rgb(25, 25, 25);\r\n    color: rgb(255, 255, 255);\r\n    border-width: 0;\r\n}\r\n\r\n.tooltip-details {\r\n    font-size: 13px;\r\n    color: rgb(197, 197, 197);\r\n    white-space: normal;\r\n    margin-top: 4px;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.tooltip-description {\r\n    font-size: 11px;\r\n    color: rgba(197, 197, 197, 0.7);\r\n    white-space: normal;\r\n    margin-top: 8px;\r\n    padding-top: 8px;\r\n    border-top-width: 1px;\r\n    border-top-color: rgba(60, 60, 60, 0.5);\r\n    font-style: italic;\r\n}\r\n\r\n#ContextMenu {\r\n    position: absolute;\r\n    background-color: rgb(0, 0, 0);\r\n    border-width: 5px;\r\n    border-color: rgb(14, 14, 14);\r\n    padding-top: 8px;\r\n    padding-bottom: 8px;\r\n    min-width: 200px;\r\n    display: none;\r\n    flex-direction: column;\r\n}\r\n\r\n#ContextMenu Button {\r\n    -unity-text-align: middle-left;\r\n    padding-left: 16px;\r\n    padding-right: 16px;\r\n    padding-top: 8px;\r\n    padding-bottom: 8px;\r\n    margin: 0;\r\n    border-width: 0;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 13px;\r\n    color: rgb(159, 159, 159);\r\n}\r\n\r\n#ContextMenu Button:hover {\r\n    background-color: rgba(33, 33, 33, 0.5);\r\n    color: rgb(255, 255, 255);\r\n}\r\n\r\n.context-menu-container {\r\n    position: absolute;\r\n    background-color: rgb(0, 0, 0);\r\n    border-top-width: 5px;\r\n    border-bottom-width: 5px;\r\n    border-left-width: 5px;\r\n    border-right-width: 5px;\r\n    border-color: rgb(14, 14, 14);\r\n    padding-top: 8px;\r\n    padding-bottom: 8px;\r\n    flex-direction: column;\r\n    min-width: 200px;\r\n}\r\n\r\n.context-name-section {\r\n    padding: 8px;\r\n    border-bottom: 1px solid rgb(58, 58, 58);\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.context-name-field {\r\n    font-size: 16px;\r\n    -unity-font-style-and-weight: bold;\r\n    background-color: rgb(25, 25, 25);\r\n    color: rgb(255, 255, 255);\r\n    border-top-width: 1px;\r\n    border-bottom-width: 1px;\r\n    border-left-width: 1px;\r\n    border-right-width: 1px;\r\n    border-color: rgb(60, 60, 60);\r\n    padding-top: 6px;\r\n    padding-bottom: 6px;\r\n    padding-left: 8px;\r\n    padding-right: 8px;\r\n}\r\n\r\n.context-name-field .unity-text-field__input {\r\n    background-color: rgb(25, 25, 25);\r\n    color: rgb(255, 255, 255);\r\n    border-width: 0;\r\n}\r\n\r\n.context-message {\r\n    font-size: 13px;\r\n    color: rgb(100, 100, 100);\r\n    padding-left: 16px;\r\n    padding-right: 16px;\r\n    padding-top: 8px;\r\n    padding-bottom: 8px;\r\n}\r\n\r\n.context-action-button {\r\n    background-color: rgb(42, 42, 42);\r\n    border-width: 1px;\r\n    border-color: rgb(68, 68, 68);\r\n    color: rgb(255, 255, 255);\r\n    margin-top: 6px;\r\n    padding: 4px 8px;\r\n    border-radius: 3px;\r\n}\r\n\r\n.context-action-button:hover {\r\n    background-color: rgb(58, 58, 58);\r\n}\r\n\r\n.context-action-button:active {\r\n    background-color: rgb(26, 26, 26);\r\n}\r\n\r\n.separator {\r\n    height: 1px;\r\n    background-color: rgb(60, 60, 60);\r\n    margin-top: 6px;\r\n    margin-bottom: 6px;\r\n    margin-left: 8px;\r\n    margin-right: 8px;\r\n}\r\n\r\n.battery-bar {\r\n    flex-grow: 1;\r\n    height: 8px;\r\n    margin-left: 16px;\r\n    flex-direction: row;\r\n    position: relative;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n    background-color: transparent;\r\n    border-width: 0;\r\n}\r\n\r\n.battery-segment-container {\r\n    width: 9%;\r\n    flex-grow: 0;\r\n    flex-shrink: 0;\r\n    height: 100%;\r\n    margin-right: 1%;\r\n    position: relative;\r\n    overflow: hidden;\r\n    border-width: 0;\r\n    border-color: transparent;\r\n    background-color: rgb(20, 20, 20);\r\n}\r\n\r\n.battery-segment-container:last-child {\r\n    margin-right: 0;\r\n}\r\n\r\n.battery-segment {\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0;\r\n    height: 100%;\r\n    width: 0%;\r\n    transition: width 0.1s ease-out;\r\n    border-width: 0;\r\n}\r\n\r\n.battery-segment-fill {\r\n    background-color: rgb(120, 120, 120);\r\n    border-radius: 1px;\r\n}\r\n\r\n.energy-notification {\r\n    position: fixed;\r\n    bottom: 60px;\r\n    left: 50%;\r\n    transform: ;\r\n    width: 400px;\r\n    padding: 8px 24px;\r\n    background-color: rgba(0, 0, 0, 0.95);\r\n    height: 40px;\r\n    justify-content: center;\r\n    border-top-width: 5px;\r\n    border-left-width: 5px;\r\n    border-right-width: 5px;\r\n    border-color: rgb(14, 14, 14);\r\n    z-index: 1000;\r\n    display: none;\r\n}\r\n\r\n.notification-battery-indicator {\r\n    height: 20px;\r\n    flex-direction: row;\r\n    align-items: center;\r\n}\r\n\r\n.battery-segment-container.show-empty-outline {\r\n    border-width: 1px;\r\n    border-color: rgba(60, 60, 60, 0.3);\r\n    background-color: rgba(30, 30, 30, 0.2);\r\n}\r\n\r\n.battery-segment-blink {\r\n    animation: blink-animation 0.5s ease-in-out infinite;\r\n}\r\n\r\n.energy-notification .battery-segment-container {\r\n    width: 9%;\r\n    flex-grow: 0;\r\n    flex-shrink: 0;\r\n    height: 10px;\r\n    border-width: 1px;\r\n    border-color: rgb(40, 40, 40);\r\n    background-color: rgb(20, 20, 20);\r\n    margin-right: 1%;\r\n}\r\n\r\n.energy-notification .battery-segment-container:last-child {\r\n    margin-right: 0;\r\n}\r\n\r\n.energy-notification .battery-segment {\r\n    border-width: 0;\r\n}\r\n\r\n.notification-battery-icon {\r\n    width: 24px;\r\n    height: 24px;\r\n    -unity-background-scale-mode: scale-to-fit;\r\n}\r\n\r\n.battery-bar {\r\n    flex-wrap: nowrap;\r\n}\r\n\r\n.notification-battery-bar {\r\n    flex-wrap: nowrap;\r\n}\r\n\r\n.battery-bar.segments-5 .battery-segment-container {\r\n    min-width: 18%;\r\n}\r\n\r\n.notification-battery-bar.segments-5 .battery-segment-container {\r\n    min-width: 18%;\r\n}\r\n\r\n.battery-bar.segments-10 .battery-segment-container {\r\n    width: 9%;\r\n    margin-right: 1%;\r\n}\r\n\r\n.battery-bar.segments-5 .battery-segment-container {\r\n    width: 18%;\r\n    margin-right: 2%;\r\n}\r\n\r\n.battery-fill--low {\r\n    display: none;\r\n}\r\n\r\n.battery-fill--medium {\r\n    display: none;\r\n}\r\n\r\n.battery-fill--high {\r\n    display: none;\r\n}\r\n\r\n.battery-segment-preview {\r\n    background-color: rgba(180, 180, 180, 0.4);\r\n    border-width: 1px;\r\n    border-color: rgba(200, 200, 200, 0.6);\r\n}\r\n\r\n.battery-segment-preview-enhance {\r\n    background-color: rgba(180, 180, 180, 0.2);\r\n}\r\n", "m_OldPath": "Assets/_Game/Scripts/Interface/Resources/inve.uss"}], "m_OpenendVisualTreeAssetOldPath": "Assets/_Game/Scripts/Interface/Resources/inve.uxml", "m_UxmlPreview": "<ui:UXML xmlns:ui=\"UnityEngine.UIElements\" xmlns:uie=\"UnityEditor.UIElements\" editor-extension-mode=\"False\">\r\n    <Style src=\"project://database/Assets/_Game/Scripts/Interface/Resources/inve.uss?fileID=7433441132597879392&amp;guid=42f6485d91c876f4b926c7584655603d&amp;type=3#inve\" />\r\n    <ui:VisualElement name=\"RootContainer\" class=\"root-container\">\r\n        <ui:VisualElement name=\"StashMainContainer\" class=\"main-container\">\r\n            <ui:VisualElement name=\"StashContainer\" class=\"panel panel--primary\">\r\n                <ui:VisualElement name=\"Header\" class=\"header\">\r\n                    <ui:VisualElement class=\"header__icon\" style=\"background-image: resource(&apos;UI/bolt&apos;);\" />\r\n                    <ui:Label text=\"STASH\" display-tooltip-when-elided=\"true\" name=\"StashTitle\" class=\"header__title\" />\r\n                    <ui:VisualElement class=\"header__icon\" style=\"background-image: resource(&apos;UI/bolt&apos;);\" />\r\n                </ui:VisualElement>\r\n                <ui:ScrollView name=\"StashScrollView\" mode=\"VerticalAndHorizontal\" vertical-scroller-visibility=\"Hidden\" horizontal-scroller-visibility=\"Hidden\" class=\"scroll-view\">\r\n                    <ui:VisualElement name=\"StashGrid\" class=\"grid-container\" />\r\n                </ui:ScrollView>\r\n            </ui:VisualElement>\r\n        </ui:VisualElement>\r\n        <ui:VisualElement name=\"InvMainContainer\" class=\"main-container\">\r\n            <ui:VisualElement name=\"InvContainer\" id=\"InvContainer\" class=\"panel panel--primary\">\r\n                <ui:VisualElement name=\"Header\" class=\"header\">\r\n                    <ui:VisualElement class=\"header__icon\" style=\"background-image: resource(&apos;UI/bolt&apos;);\" />\r\n                    <ui:Label text=\"€ 000000000\" display-tooltip-when-elided=\"true\" name=\"CurrencyLabel\" class=\"header__money\" />\r\n                    <ui:VisualElement class=\"header__icon\" style=\"background-image: resource(&apos;UI/bolt&apos;);\" />\r\n                </ui:VisualElement>\r\n                <ui:VisualElement name=\"EquipmentArea\" class=\"equipment-area\">\r\n                    <ui:VisualElement name=\"EquipmentSlots\" class=\"equipment-slots\">\r\n                        <ui:VisualElement name=\"HeadSlot\" id=\"HeadSlot\" class=\"equipment-slot empty\" style=\"width: 124px; height: 124px; min-width: 124px; min-height: 124px; max-height: 124px; max-width: 124px; margin-right: 8px; align-items: center; justify-content: center;\">\r\n                            <ui:Image name=\"HeadSlotIcon\" id=\"HeadSlotIcon\" class=\"equipment-slot-img\" style=\"background-image: url(&quot;project://database/Assets/_Game/Resources/UI/Placeholder_Helmet.png?fileID=21300000&amp;guid=dec077adde63d60419133ee65217fc44&amp;type=3#Placeholder_Helmet&quot;);\" />\r\n                        </ui:VisualElement>\r\n                        <ui:VisualElement name=\"ChestSlot\" id=\"ChestSlot\" class=\"equipment-slot empty\" style=\"width: 124px; height: 124px; min-height: 124px; min-width: 124px; max-width: 124px; max-height: 124px; margin-right: 8px; align-items: center; justify-content: center;\">\r\n                            <ui:Image name=\"ChestSlotIcon\" id=\"ChestSlotIcon\" class=\"equipment-slot-img\" style=\"background-image: url(&quot;project://database/Assets/_Game/Resources/UI/Placeholder_Chest.png?fileID=21300000&amp;guid=cfe99bfd62c310048889033c2aaf6c93&amp;type=3#Placeholder_Chest&quot;);\" />\r\n                        </ui:VisualElement>\r\n                        <ui:VisualElement name=\"BagSlot\" id=\"BagSlot\" class=\"equipment-slot empty\" style=\"max-width: 124px; min-width: 124px; min-height: 124px; max-height: 124px; height: 124px; width: 124px; align-items: center; justify-content: center;\">\r\n                            <ui:Image name=\"BagSlotIcon\" id=\"BagSlotIcon\" class=\"equipment-slot-img\" style=\"background-image: url(&quot;project://database/Assets/_Game/Resources/UI/Placeholder_Bag.png?fileID=21300000&amp;guid=74743076af2b40a4c8f093981a264bdc&amp;type=3#Placeholder_Bag&quot;);\" />\r\n                        </ui:VisualElement>\r\n                    </ui:VisualElement>\r\n                </ui:VisualElement>\r\n                <ui:ScrollView name=\"Inv\" mode=\"Vertical\" class=\"scroll-view\">\r\n                    <ui:VisualElement name=\"InvPlaceholder\" class=\"inventory-placeholder\">\r\n                        <ui:Label text=\"Equip a bag to access inventory storage\" class=\"inventory-placeholder-text\" />\r\n                    </ui:VisualElement>\r\n                    <ui:VisualElement name=\"InvGrid\" class=\"grid-container\" />\r\n                </ui:ScrollView>\r\n                <ui:VisualElement name=\"Stats\" class=\"stats\" style=\"display: none;\">\r\n                    <ui:Label tabindex=\"-1\" text=\"0/0\" display-tooltip-when-elided=\"true\" name=\"WeightLabel\" enable-rich-text=\"false\" class=\"stats__label stats__weight\" />\r\n                    <ui:Label tabindex=\"-1\" text=\"0/100\" display-tooltip-when-elided=\"true\" name=\"EnergyLabel\" class=\"stats__label stats__energy\" />\r\n                </ui:VisualElement>\r\n                <ui:VisualElement name=\"EnergyContainer\" class=\"energy-container\">\r\n                    <ui:VisualElement name=\"BatteryIndicator\" class=\"battery-indicator\">\r\n                        <ui:VisualElement name=\"BatteryIcon\" class=\"battery-icon\" />\r\n                        <ui:VisualElement name=\"BatteryBar\" class=\"battery-bar\" />\r\n                    </ui:VisualElement>\r\n                </ui:VisualElement>\r\n            </ui:VisualElement>\r\n            <ui:VisualElement name=\"ShopContainer\" class=\"panel panel--secondary\">\r\n                <ui:VisualElement name=\"Header\" class=\"header\">\r\n                    <ui:VisualElement class=\"header__icon\" style=\"background-image: resource(&apos;UI/bolt&apos;);\" />\r\n                    <ui:Label text=\"TRADER\" display-tooltip-when-elided=\"true\" name=\"ShopTitle\" class=\"header__title\" />\r\n                    <ui:VisualElement class=\"header__icon\" style=\"background-image: resource(&apos;UI/bolt&apos;);\" />\r\n                </ui:VisualElement>\r\n                <ui:VisualElement name=\"ShopModeControls\" class=\"shop-controls\">\r\n                    <ui:Button text=\"Buy\" name=\"BuyModeButton\" class=\"pixel-button pixel-button--mode pixel-button--active\" />\r\n                    <ui:Button text=\"Sell\" name=\"SellModeButton\" class=\"pixel-button pixel-button--mode\" />\r\n                </ui:VisualElement>\r\n                <ui:VisualElement name=\"BuyModeContainer\" class=\"mode-container\">\r\n                    <ui:ScrollView name=\"ShopScrollView\" vertical-scroller-visibility=\"Auto\" horizontal-scroller-visibility=\"Hidden\" class=\"scroll-view\">\r\n                        <ui:VisualElement name=\"ShopGrid\" class=\"grid-container\" />\r\n                    </ui:ScrollView>\r\n                    <ui:VisualElement name=\"ShopFooter\" class=\"shop-footer\">\r\n                        <ui:VisualElement name=\"ValueInfo\" class=\"value-info\">\r\n                            <ui:Label text=\"€ 0\" name=\"BuyTotalValue\" class=\"label-value\" style=\"color: rgb(153, 153, 153);\" />\r\n                        </ui:VisualElement>\r\n                        <ui:Button text=\"Buy\" name=\"BuyButton\" enabled=\"false\" class=\"pixel-button pixel-button--buy disabled\" />\r\n                    </ui:VisualElement>\r\n                </ui:VisualElement>\r\n                <ui:VisualElement name=\"SellModeContainer\" class=\"mode-container\" style=\"display: none;\">\r\n                    <ui:ScrollView name=\"SellGridScrollView\" vertical-scroller-visibility=\"Auto\" horizontal-scroller-visibility=\"Hidden\" mode=\"VerticalAndHorizontal\" class=\"scroll-view\" style=\"padding-top: 0; padding-right: 0; padding-bottom: 0; padding-left: 0;\">\r\n                        <ui:VisualElement name=\"SellGrid\" class=\"grid-container\" />\r\n                    </ui:ScrollView>\r\n                    <ui:VisualElement name=\"SellFooter\" class=\"shop-footer\">\r\n                        <ui:VisualElement name=\"ValueInfo\" class=\"value-info\">\r\n                            <ui:Label text=\"€ 0\" name=\"SellTotalValue\" class=\"label-value\" style=\"color: rgb(153, 153, 153);\" />\r\n                        </ui:VisualElement>\r\n                        <ui:Button text=\"Sell\" name=\"SellButton\" enabled=\"false\" class=\"pixel-button pixel-button--sell disabled\" />\r\n                    </ui:VisualElement>\r\n                </ui:VisualElement>\r\n                <ui:VisualElement name=\"InstructionsContainer\" class=\"instructions-container\" style=\"height: 40px;\">\r\n                    <ui:VisualElement name=\"BuyInstructionsContainer\" class=\"instructions-wrapper\">\r\n                        <ui:Label text=\"Click any item/s to select to buy\" display-tooltip-when-elided=\"true\" name=\"BuyInstructionText\" class=\"instruction-text\" />\r\n                    </ui:VisualElement>\r\n                    <ui:VisualElement name=\"SellInstructionsContainer\" class=\"instructions-wrapper\" style=\"display: none;\">\r\n                        <ui:Label text=\"Click any item/s to select to sell\" display-tooltip-when-elided=\"true\" name=\"SellInstructionText\" class=\"instruction-text\" />\r\n                    </ui:VisualElement>\r\n                </ui:VisualElement>\r\n            </ui:VisualElement>\r\n        </ui:VisualElement>\r\n    </ui:VisualElement>\r\n</ui:UXML>\r\n", "m_ContentHash": 1101312912, "m_VisualTreeAssetRef": {"fileID": 9197481963319205126, "guid": "12c8171353d210a4488f806368fcb07b", "type": 3}, "m_ActiveStyleSheet": {"instanceID": 0}, "m_Settings": {"UxmlGuid": "12c8171353d210a4488f806368fcb07b", "UxmlPath": "Assets/_Game/Scripts/Inv/inve.uxml", "CanvasX": -524, "CanvasY": -168, "CanvasWidth": 1833, "CanvasHeight": 1144, "MatchGameView": false, "ZoomScale": 1.350000023841858, "PanOffset": {"x": 144.8670654296875, "y": -619.7564697265625}, "ColorModeBackgroundOpacity": 1.0, "ImageModeCanvasBackgroundOpacity": 1.0, "CameraModeCanvasBackgroundOpacity": 1.0, "EnableCanvasBackground": false, "CanvasBackgroundMode": 0, "CanvasBackgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "CanvasBackgroundImage": {"instanceID": 0}, "CanvasBackgroundImageScaleMode": 1, "CanvasBackgroundCameraName": ""}, "m_OpenSubDocumentParentIndex": -1, "m_OpenSubDocumentParentSourceTemplateAssetIndex": -1}], "m_ActiveOpenUXMLFileIndex": 0}}